#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للحساب الذي يعمل
Quick test for working account
"""

import os
import sys
import time

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الدوال من الملف الرئيسي
from shahid import setup_chrome_driver, login_shahid, clear_browser_data

def test_working_account():
    """اختبار سريع للحساب الذي يعمل"""
    print("🧪 اختبار سريع للحساب الذي يعمل...")
    
    # الحساب الذي يعمل
    username = "<EMAIL>"
    password = "Hh1122334455"
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(driver_path)
        
        print("🚀 بدء تشغيل Chrome...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print(f"\n{'='*60}")
        print(f"🔍 اختبار الحساب: {username}")
        print(f"{'='*60}")
        
        # محو بيانات التصفح أولاً
        print("🧹 محو بيانات التصفح...")
        clear_result = clear_browser_data(driver)
        print(f"🗑️ نتيجة المحو: {clear_result}")
        
        # محاولة تسجيل الدخول
        print("\n🔐 محاولة تسجيل الدخول...")
        login_status = login_shahid(driver, username, password)
        
        print(f"\n{'='*60}")
        print("📊 النتيجة النهائية:")
        print(f"{'='*60}")
        print(f"🔍 الحساب: {username}")
        print(f"📊 نتيجة تسجيل الدخول: {login_status}")
        
        if login_status == "success":
            print("🎉 ممتاز! الحساب يعمل والبرنامج يتعرف عليه بشكل صحيح!")
            result = True
        elif login_status == "fail":
            print("❌ مشكلة: الحساب يعمل لكن البرنامج يعتبره فاشل!")
            print("🔧 يحتاج مزيد من التحسين في منطق اكتشاف النجاح")
            result = False
        elif login_status == "unknown":
            print("❓ حالة غامضة - يحتاج فحص يدوي")
            print("👀 تحقق من الصفحة الحالية بصرياً")
            
            # معلومات إضافية
            try:
                current_url = driver.current_url
                page_title = driver.title
                print(f"🔗 الرابط الحالي: {current_url}")
                print(f"📄 عنوان الصفحة: {page_title}")
            except:
                pass
            
            user_input = input("هل تم تسجيل الدخول بنجاح؟ (y/n): ").lower().strip()
            if user_input in ['y', 'yes', 'نعم', 'ن']:
                print("✅ تأكيد: الحساب يعمل!")
                result = True
            else:
                print("❌ تأكيد: الحساب لا يعمل!")
                result = False
        else:
            print(f"❓ نتيجة غير متوقعة: {login_status}")
            result = False
        
        # إبقاء المتصفح مفتوح للمراجعة
        print(f"\n🔍 المتصفح مفتوح للمراجعة...")
        input("اضغط Enter لإغلاق المتصفح والخروج...")
        driver.quit()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار سريع للحساب الذي يعمل")
    print("=" * 60)
    
    print("🎯 الهدف: التأكد من أن البرنامج يتعرف على الحساب الصحيح")
    print("📧 الحساب: <EMAIL>:Hh1122334455")
    print()
    
    if test_working_account():
        print("\n🎉 نجح الاختبار! البرنامج يتعرف على الحساب بشكل صحيح!")
        print("✅ يمكنك الآن استخدام البرنامج الرئيسي بثقة")
    else:
        print("\n❌ فشل الاختبار! هناك مشكلة في اكتشاف النجاح")
        print("🔧 يحتاج مزيد من التحسين")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    main()
