#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار النظام الجديد للحفظ المباشر
Test new direct save system
"""

import os
import sys
import time

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_save_system():
    """اختبار النظام الجديد للحفظ المباشر"""
    print("🧪 اختبار النظام الجديد للحفظ المباشر...")
    
    try:
        # استيراد الدوال الجديدة
        from shahid import initialize_good_accounts_file, save_good_account_immediately, OUTPUT_FILES
        
        print("✅ تم استيراد الدوال الجديدة")
        
        # اختبار إنشاء ملف فارغ
        print("\n📁 اختبار إنشاء ملف good.txt فارغ...")
        result = initialize_good_accounts_file()
        
        if result:
            print("✅ تم إنشاء الملف بنجاح")
            
            # التحقق من وجود الملف
            if os.path.exists(OUTPUT_FILES['good_accounts']):
                print(f"✅ الملف موجود: {OUTPUT_FILES['good_accounts']}")
                
                # التحقق من أن الملف فارغ
                with open(OUTPUT_FILES['good_accounts'], 'r', encoding='utf-8') as f:
                    content = f.read()
                    if content == "":
                        print("✅ الملف فارغ كما هو متوقع")
                    else:
                        print(f"⚠️ الملف ليس فارغ: '{content}'")
            else:
                print("❌ الملف غير موجود!")
                return False
        else:
            print("❌ فشل في إنشاء الملف")
            return False
        
        # اختبار حفظ حسابات صحيحة
        print("\n💾 اختبار حفظ الحسابات الصحيحة...")
        test_accounts = [
            "<EMAIL>:password1",
            "<EMAIL>:password2", 
            "<EMAIL>:password3"
        ]
        
        for i, account in enumerate(test_accounts, 1):
            print(f"[{i}/{len(test_accounts)}] حفظ: {account}")
            result = save_good_account_immediately(account)
            
            if result:
                print(f"✅ تم حفظ الحساب {i}")
            else:
                print(f"❌ فشل في حفظ الحساب {i}")
                return False
            
            # انتظار قصير
            time.sleep(0.5)
        
        # التحقق من محتوى الملف النهائي
        print("\n📄 فحص محتوى الملف النهائي...")
        if os.path.exists(OUTPUT_FILES['good_accounts']):
            with open(OUTPUT_FILES['good_accounts'], 'r', encoding='utf-8') as f:
                content = f.read().strip()
                lines = content.split('\n') if content else []
                
                print(f"📊 عدد الأسطر في الملف: {len(lines)}")
                print(f"📄 محتوى الملف:")
                for i, line in enumerate(lines, 1):
                    print(f"  {i}. {line}")
                
                # التحقق من صحة المحتوى
                if len(lines) == len(test_accounts):
                    print("✅ عدد الأسطر صحيح")
                    
                    all_correct = True
                    for i, (expected, actual) in enumerate(zip(test_accounts, lines)):
                        if expected == actual:
                            print(f"✅ السطر {i+1} صحيح")
                        else:
                            print(f"❌ السطر {i+1} خطأ: متوقع '{expected}' لكن وجد '{actual}'")
                            all_correct = False
                    
                    if all_correct:
                        print("🎉 جميع الحسابات تم حفظها بشكل صحيح!")
                        return True
                    else:
                        print("❌ هناك أخطاء في المحتوى")
                        return False
                else:
                    print(f"❌ عدد الأسطر خطأ: متوقع {len(test_accounts)} لكن وجد {len(lines)}")
                    return False
        else:
            print("❌ الملف غير موجود!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_no_temp_files():
    """اختبار عدم إنشاء ملفات مؤقتة"""
    print("\n🗑️ اختبار عدم إنشاء ملفات مؤقتة...")
    
    import glob
    
    # البحث عن ملفات مؤقتة قبل الاختبار
    temp_files_before = glob.glob("temp_good_*.txt") + glob.glob("temp_bad_*.txt")
    print(f"📁 ملفات مؤقتة قبل الاختبار: {len(temp_files_before)}")
    
    # تشغيل النظام الجديد
    test_new_save_system()
    
    # البحث عن ملفات مؤقتة بعد الاختبار
    temp_files_after = glob.glob("temp_good_*.txt") + glob.glob("temp_bad_*.txt")
    print(f"📁 ملفات مؤقتة بعد الاختبار: {len(temp_files_after)}")
    
    if len(temp_files_after) == len(temp_files_before):
        print("✅ لم يتم إنشاء ملفات مؤقتة جديدة!")
        return True
    else:
        print("❌ تم إنشاء ملفات مؤقتة جديدة!")
        new_files = set(temp_files_after) - set(temp_files_before)
        for file in new_files:
            print(f"  📄 ملف جديد: {file}")
        return False

def cleanup_test_files():
    """تنظيف ملفات الاختبار"""
    print("\n🧹 تنظيف ملفات الاختبار...")
    
    try:
        from shahid import OUTPUT_FILES
        
        # حذف ملف good.txt إذا كان موجود
        if os.path.exists(OUTPUT_FILES['good_accounts']):
            os.remove(OUTPUT_FILES['good_accounts'])
            print(f"🗑️ تم حذف {OUTPUT_FILES['good_accounts']}")
        
        # حذف أي ملفات مؤقتة
        import glob
        temp_files = glob.glob("temp_good_*.txt") + glob.glob("temp_bad_*.txt")
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
                print(f"🗑️ تم حذف الملف المؤقت: {temp_file}")
            except:
                pass
        
        print("✅ تم التنظيف بنجاح")
        
    except Exception as e:
        print(f"⚠️ خطأ في التنظيف: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار النظام الجديد للحفظ المباشر")
    print("=" * 60)
    
    print("🎯 الهدف: التأكد من أن النظام الجديد يعمل بدون ملفات مؤقتة")
    print("💾 النظام الجديد: حفظ مباشر في good.txt فقط")
    print()
    
    success = True
    
    # اختبار النظام الجديد
    if test_new_save_system():
        print("\n✅ نجح اختبار النظام الجديد!")
    else:
        print("\n❌ فشل اختبار النظام الجديد!")
        success = False
    
    # اختبار عدم إنشاء ملفات مؤقتة
    if test_no_temp_files():
        print("\n✅ نجح اختبار عدم إنشاء ملفات مؤقتة!")
    else:
        print("\n❌ فشل اختبار عدم إنشاء ملفات مؤقتة!")
        success = False
    
    # تنظيف الملفات
    cleanup_test_files()
    
    print("\n" + "="*60)
    if success:
        print("🎉 جميع الاختبارات نجحت! النظام الجديد يعمل بشكل مثالي!")
        print("✅ لا مزيد من الملفات المؤقتة!")
        print("💾 حفظ مباشر للحسابات الصحيحة في good.txt")
    else:
        print("❌ بعض الاختبارات فشلت! يحتاج مراجعة")
    print("="*60)

if __name__ == "__main__":
    main()
