#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحقق الدقيق من النتائج
Test accurate validation of results
"""

import os
import sys
import time

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الدوال من الملف الرئيسي
from shahid import setup_chrome_driver, login_shahid, clear_browser_data, save_results_automatically

def test_error_detection():
    """اختبار اكتشاف رسائل الخطأ"""
    print("🧪 اختبار اكتشاف رسائل الخطأ...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(driver_path)
        
        print("🚀 بدء تشغيل Chrome...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # حسابات اختبار - يجب أن تكون خاطئة
        test_accounts = [
            ("<EMAIL>", "Muhtadi20021"),  # الحساب من الصورة
            ("<EMAIL>", "wrong_password"),
            ("<EMAIL>", "123456"),
        ]
        
        results = []
        
        for i, (username, password) in enumerate(test_accounts, 1):
            print(f"\n{'='*60}")
            print(f"🔍 اختبار الحساب {i}: {username}")
            print(f"{'='*60}")
            
            try:
                # محو بيانات التصفح قبل كل اختبار
                if i > 1:
                    print("🧹 محو بيانات التصفح...")
                    clear_result = clear_browser_data(driver)
                    print(f"🗑️ نتيجة المحو: {clear_result}")
                
                # محاولة تسجيل الدخول
                print(f"🔐 محاولة تسجيل الدخول...")
                login_status = login_shahid(driver, username, password)
                print(f"📊 نتيجة تسجيل الدخول: {login_status}")
                
                # تحليل النتيجة
                if login_status == "success":
                    print("⚠️ تحذير: الحساب يظهر كناجح لكن يجب التحقق يدوياً!")
                    result_type = "false_positive"
                elif login_status == "fail":
                    print("✅ ممتاز: تم اكتشاف فشل تسجيل الدخول بشكل صحيح")
                    result_type = "correct_failure"
                else:
                    print(f"❓ نتيجة غير متوقعة: {login_status}")
                    result_type = "unexpected"
                
                results.append({
                    'account': username,
                    'password': password,
                    'login_status': login_status,
                    'result_type': result_type
                })
                
                # انتظار قبل الحساب التالي
                print("⏳ انتظار قبل الحساب التالي...")
                time.sleep(3)
                
            except Exception as e:
                print(f"❌ خطأ في اختبار {username}: {e}")
                results.append({
                    'account': username,
                    'password': password,
                    'error': str(e)
                })
        
        print("\n" + "="*60)
        print("📊 ملخص نتائج الاختبار:")
        print("="*60)
        
        false_positives = 0
        correct_failures = 0
        unexpected_results = 0
        
        for i, result in enumerate(results, 1):
            print(f"\n🔍 الحساب {i}: {result['account']}")
            if 'error' in result:
                print(f"   ❌ خطأ: {result['error']}")
            else:
                print(f"   🔐 كلمة المرور: {result['password']}")
                print(f"   📊 نتيجة تسجيل الدخول: {result['login_status']}")
                print(f"   🎯 تحليل النتيجة: {result['result_type']}")
                
                if result['result_type'] == 'false_positive':
                    false_positives += 1
                elif result['result_type'] == 'correct_failure':
                    correct_failures += 1
                else:
                    unexpected_results += 1
        
        print(f"\n📈 تحليل النتائج:")
        print(f"   ✅ اكتشاف صحيح للفشل: {correct_failures}")
        print(f"   ⚠️ نتائج إيجابية خاطئة: {false_positives}")
        print(f"   ❓ نتائج غير متوقعة: {unexpected_results}")
        
        if false_positives == 0:
            print("   🎉 ممتاز! لا توجد نتائج إيجابية خاطئة!")
        else:
            print("   ⚠️ تحذير: هناك نتائج إيجابية خاطئة تحتاج لمعالجة")
        
        # إبقاء المتصفح مفتوح للمراجعة اليدوية
        print("\n🔍 المتصفح مفتوح للمراجعة اليدوية...")
        input("اضغط Enter لإغلاق المتصفح والخروج...")
        driver.quit()
        
        return false_positives == 0
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_auto_save_feature():
    """اختبار ميزة الحفظ التلقائي"""
    print("🧪 اختبار ميزة الحفظ التلقائي...")
    
    try:
        # إنشاء بيانات اختبار
        good_accounts = [
            "<EMAIL>:password1",
            "<EMAIL>:password2"
        ]
        
        bad_accounts = [
            "<EMAIL>:wrongpass => fail",
            "<EMAIL>:wrongpass => fail"
        ]
        
        # اختبار الحفظ التلقائي
        print("💾 اختبار الحفظ التلقائي...")
        result = save_results_automatically(good_accounts, bad_accounts, 2, 10)
        
        if result:
            print("✅ نجح الحفظ التلقائي!")
            
            # فحص الملفات المؤقتة
            import glob
            temp_good_files = glob.glob("temp_good_*.txt")
            temp_bad_files = glob.glob("temp_bad_*.txt")
            
            print(f"📁 ملفات الحسابات الصالحة المؤقتة: {len(temp_good_files)}")
            print(f"📁 ملفات الحسابات السيئة المؤقتة: {len(temp_bad_files)}")
            
            # قراءة محتوى الملفات للتحقق
            if temp_good_files:
                with open(temp_good_files[0], 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"📄 محتوى ملف الحسابات الصالحة:\n{content}")
            
            if temp_bad_files:
                with open(temp_bad_files[0], 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"📄 محتوى ملف الحسابات السيئة:\n{content}")
            
            # تنظيف الملفات المؤقتة
            for temp_file in temp_good_files + temp_bad_files:
                try:
                    os.remove(temp_file)
                    print(f"🗑️ تم حذف الملف المؤقت: {temp_file}")
                except:
                    pass
            
            return True
        else:
            print("❌ فشل الحفظ التلقائي!")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الحفظ التلقائي: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار التحقق الدقيق من النتائج")
    print("=" * 60)
    
    print("⚠️ هذا الاختبار مخصص للتحقق من:")
    print("   1. اكتشاف رسائل الخطأ بدقة")
    print("   2. تجنب النتائج الإيجابية الخاطئة")
    print("   3. اختبار ميزة الحفظ التلقائي")
    print()
    
    # اختبار ميزة الحفظ التلقائي
    print("1️⃣ اختبار ميزة الحفظ التلقائي...")
    if test_auto_save_feature():
        print("✅ اختبار الحفظ التلقائي نجح!")
    else:
        print("❌ اختبار الحفظ التلقائي فشل!")
        return
    
    print("\n" + "="*60)
    response = input("هل تريد اختبار اكتشاف رسائل الخطأ؟ (y/n): ").lower().strip()
    
    if response in ['y', 'yes', 'نعم', 'ن']:
        print("2️⃣ اختبار اكتشاف رسائل الخطأ...")
        if test_error_detection():
            print("✅ اختبار اكتشاف الأخطاء نجح!")
        else:
            print("❌ اختبار اكتشاف الأخطاء فشل!")
    else:
        print("✅ تم تخطي اختبار اكتشاف الأخطاء")
    
    print("\n" + "="*60)
    print("🎉 انتهى الاختبار!")
    print("💡 إذا نجحت الاختبارات، فالبرنامج يتحقق من النتائج بدقة ويحفظ تلقائياً")
    print("="*60)

if __name__ == "__main__":
    main()
