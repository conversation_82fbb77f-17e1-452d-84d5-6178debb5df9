#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار محو بيانات التصفح
Test clearing browser data
"""

import os
import sys
import time

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الدوال من الملف الرئيسي
from shahid import setup_chrome_driver, clear_browser_data, check_login_status

def test_clear_browser_data():
    """اختبار محو بيانات التصفح"""
    print("🧪 اختبار محو بيانات التصفح...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(driver_path)
        
        print("🚀 بدء تشغيل Chrome...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # الانتقال إلى موقع Shahid
        print("🌐 الانتقال إلى موقع Shahid...")
        driver.get("https://shahid.mbc.net")
        time.sleep(5)
        
        # فحص الحالة الأولية
        print("1️⃣ فحص الحالة الأولية...")
        initial_status = check_login_status(driver)
        print(f"   📊 الحالة الأولية: {initial_status}")
        
        # إضافة بعض البيانات للتصفح (cookies, localStorage)
        print("2️⃣ إضافة بيانات اختبار...")
        try:
            # إضافة cookie اختبار
            driver.add_cookie({"name": "test_cookie", "value": "test_value"})
            print("   🍪 تم إضافة cookie اختبار")
            
            # إضافة localStorage اختبار
            driver.execute_script("localStorage.setItem('test_key', 'test_value');")
            print("   💾 تم إضافة localStorage اختبار")
            
            # إضافة sessionStorage اختبار
            driver.execute_script("sessionStorage.setItem('test_session', 'test_session_value');")
            print("   📝 تم إضافة sessionStorage اختبار")
            
        except Exception as e:
            print(f"   ⚠️ خطأ في إضافة بيانات الاختبار: {e}")
        
        # فحص البيانات قبل المحو
        print("3️⃣ فحص البيانات قبل المحو...")
        try:
            cookies_before = len(driver.get_cookies())
            localStorage_before = driver.execute_script("return localStorage.length;")
            sessionStorage_before = driver.execute_script("return sessionStorage.length;")
            
            print(f"   🍪 عدد Cookies: {cookies_before}")
            print(f"   💾 عدد localStorage items: {localStorage_before}")
            print(f"   📝 عدد sessionStorage items: {sessionStorage_before}")
            
        except Exception as e:
            print(f"   ⚠️ خطأ في فحص البيانات: {e}")
        
        # اختبار محو بيانات التصفح
        print("4️⃣ اختبار محو بيانات التصفح...")
        clear_result = clear_browser_data(driver)
        print(f"   🗑️ نتيجة محو البيانات: {clear_result}")
        
        # فحص البيانات بعد المحو
        print("5️⃣ فحص البيانات بعد المحو...")
        try:
            cookies_after = len(driver.get_cookies())
            localStorage_after = driver.execute_script("return localStorage.length;")
            sessionStorage_after = driver.execute_script("return sessionStorage.length;")
            
            print(f"   🍪 عدد Cookies: {cookies_after}")
            print(f"   💾 عدد localStorage items: {localStorage_after}")
            print(f"   📝 عدد sessionStorage items: {sessionStorage_after}")
            
            # تحليل النتائج
            cookies_cleared = cookies_before > cookies_after
            localStorage_cleared = localStorage_before > localStorage_after
            sessionStorage_cleared = sessionStorage_before > sessionStorage_after
            
            print(f"\n📊 تحليل النتائج:")
            print(f"   🍪 Cookies محذوفة: {'✅ نعم' if cookies_cleared else '❌ لا'}")
            print(f"   💾 localStorage محذوف: {'✅ نعم' if localStorage_cleared else '❌ لا'}")
            print(f"   📝 sessionStorage محذوف: {'✅ نعم' if sessionStorage_cleared else '❌ لا'}")
            
        except Exception as e:
            print(f"   ⚠️ خطأ في فحص البيانات بعد المحو: {e}")
        
        # فحص الحالة النهائية
        print("6️⃣ فحص الحالة النهائية...")
        final_status = check_login_status(driver)
        print(f"   📊 الحالة النهائية: {final_status}")
        
        # مقارنة الحالات
        if initial_status != final_status:
            print(f"✅ تغيرت الحالة من {initial_status} إلى {final_status}")
        else:
            print(f"ℹ️ الحالة لم تتغير: {initial_status}")
        
        print("\n" + "="*60)
        print("📊 ملخص الاختبار:")
        print("="*60)
        print(f"🔍 الحالة الأولية: {initial_status}")
        print(f"🗑️ نتيجة محو البيانات: {clear_result}")
        print(f"📊 الحالة النهائية: {final_status}")
        
        if clear_result == "success":
            print("🎉 نجح اختبار محو بيانات التصفح!")
        else:
            print("⚠️ هناك مشاكل في محو بيانات التصفح")
        
        # إبقاء المتصفح مفتوح للمراجعة اليدوية
        print("\n🔍 المتصفح مفتوح للمراجعة اليدوية...")
        input("اضغط Enter لإغلاق المتصفح والخروج...")
        driver.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_multiple_clear_operations():
    """اختبار عمليات محو متعددة"""
    print("🧪 اختبار عمليات محو متعددة...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # اختبار عمليات محو متعددة
        for i in range(1, 4):
            print(f"\n{'='*50}")
            print(f"🔄 عملية المحو رقم {i}")
            print(f"{'='*50}")
            
            # الانتقال إلى الموقع
            driver.get("https://shahid.mbc.net")
            time.sleep(3)
            
            # فحص الحالة
            status_before = check_login_status(driver)
            print(f"📊 الحالة قبل المحو: {status_before}")
            
            # محو البيانات
            clear_result = clear_browser_data(driver)
            print(f"🗑️ نتيجة المحو: {clear_result}")
            
            # فحص الحالة بعد المحو
            status_after = check_login_status(driver)
            print(f"📊 الحالة بعد المحو: {status_after}")
            
            # انتظار قبل العملية التالية
            if i < 3:
                print("⏳ انتظار قبل العملية التالية...")
                time.sleep(2)
        
        print("\n🎉 انتهى اختبار العمليات المتعددة!")
        
        # إبقاء المتصفح مفتوح للمراجعة اليدوية
        print("\n🔍 المتصفح مفتوح للمراجعة اليدوية...")
        input("اضغط Enter لإغلاق المتصفح والخروج...")
        driver.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار محو بيانات التصفح")
    print("=" * 60)
    
    print("⚠️ هذا الاختبار مخصص لاختبار دالة محو بيانات التصفح")
    print("⚠️ سيتم اختبار محو Cookies, localStorage, sessionStorage")
    print()
    
    # اختبار أساسي
    print("1️⃣ اختبار أساسي لمحو بيانات التصفح...")
    if test_clear_browser_data():
        print("✅ الاختبار الأساسي نجح!")
    else:
        print("❌ الاختبار الأساسي فشل!")
        return
    
    print("\n" + "="*60)
    response = input("هل تريد اختبار عمليات محو متعددة؟ (y/n): ").lower().strip()
    
    if response in ['y', 'yes', 'نعم', 'ن']:
        print("2️⃣ اختبار عمليات محو متعددة...")
        if test_multiple_clear_operations():
            print("✅ اختبار العمليات المتعددة نجح!")
        else:
            print("❌ اختبار العمليات المتعددة فشل!")
    else:
        print("✅ تم تخطي اختبار العمليات المتعددة")
    
    print("\n" + "="*60)
    print("🎉 انتهى الاختبار!")
    print("💡 إذا نجحت الاختبارات، فدالة محو بيانات التصفح تعمل بشكل صحيح")
    print("="*60)

if __name__ == "__main__":
    main()
