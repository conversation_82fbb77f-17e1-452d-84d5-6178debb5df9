#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حساب محدد
Test specific account
"""

import os
import sys
import time

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الدوال من الملف الرئيسي
from shahid import setup_chrome_driver, login_shahid, clear_browser_data, check_login_status

def test_specific_account():
    """اختبار حساب محدد"""
    print("🧪 اختبار حساب محدد...")
    
    # الحساب الذي يعمل لكن يظهر كـ bad
    username = "<EMAIL>"
    password = "Hh1122334455"
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(driver_path)
        
        print("🚀 بدء تشغيل Chrome...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print(f"\n{'='*60}")
        print(f"🔍 اختبار الحساب: {username}")
        print(f"🔐 كلمة المرور: {password}")
        print(f"{'='*60}")
        
        # محو بيانات التصفح أولاً
        print("🧹 محو بيانات التصفح...")
        clear_result = clear_browser_data(driver)
        print(f"🗑️ نتيجة المحو: {clear_result}")
        
        # فحص الحالة الأولية
        print("\n1️⃣ فحص الحالة الأولية...")
        initial_status = check_login_status(driver)
        print(f"📊 الحالة الأولية: {initial_status}")
        
        # محاولة تسجيل الدخول
        print("\n2️⃣ محاولة تسجيل الدخول...")
        login_status = login_shahid(driver, username, password)
        print(f"📊 نتيجة تسجيل الدخول: {login_status}")
        
        # فحص الحالة النهائية
        print("\n3️⃣ فحص الحالة النهائية...")
        final_status = check_login_status(driver)
        print(f"📊 الحالة النهائية: {final_status}")
        
        # تحليل النتائج
        print(f"\n{'='*60}")
        print("📊 تحليل النتائج:")
        print(f"{'='*60}")
        print(f"🔍 الحساب: {username}")
        print(f"🔐 كلمة المرور: {password}")
        print(f"📊 نتيجة تسجيل الدخول: {login_status}")
        print(f"📊 الحالة النهائية: {final_status}")
        
        # معلومات إضافية للتشخيص
        current_url = driver.current_url
        page_title = driver.title
        print(f"🔗 الرابط الحالي: {current_url}")
        print(f"📄 عنوان الصفحة: {page_title}")
        
        # فحص محتوى الصفحة
        page_source = driver.page_source.lower()
        
        # فحص علامات النجاح
        success_keywords = [
            "logout", "خروج", "تسجيل خروج", "profile", "profiles", 
            "مرحباً اختر من يشاهد", "اختر من يشاهد", "إدارة الملفات",
            "أطفال", "ملف شخصي", "الملف الشخصي"
        ]
        
        found_success_keywords = []
        for keyword in success_keywords:
            if keyword in page_source or keyword in current_url.lower():
                found_success_keywords.append(keyword)
        
        if found_success_keywords:
            print(f"✅ علامات النجاح الموجودة: {found_success_keywords}")
        else:
            print("❌ لم يتم العثور على علامات نجاح")
        
        # فحص علامات الفشل
        fail_keywords = [
            "incorrect", "خطأ", "invalid", "wrong", "غير صحيح", "غير صحيحة",
            "فشل", "error", "بيانات غير صحيحة", "كلمة المرور خاطئة",
            "البريد الإلكتروني غير صحيح", "اسم مستخدم أو كلمة سر غير صحيحة"
        ]
        
        found_fail_keywords = []
        for keyword in fail_keywords:
            if keyword in page_source:
                found_fail_keywords.append(keyword)
        
        if found_fail_keywords:
            print(f"❌ علامات الفشل الموجودة: {found_fail_keywords}")
        else:
            print("✅ لم يتم العثور على علامات فشل")
        
        # التوصية
        print(f"\n💡 التوصية:")
        if login_status == "success":
            print("   ✅ الحساب يعمل بشكل صحيح!")
        elif found_success_keywords and not found_fail_keywords:
            print("   ⚠️ الحساب يبدو أنه يعمل لكن البرنامج لم يتعرف عليه!")
            print("   🔧 يحتاج تحسين في منطق اكتشاف النجاح")
        elif found_fail_keywords:
            print("   ❌ الحساب فعلاً لا يعمل - بيانات خاطئة")
        else:
            print("   ❓ حالة غير واضحة - يحتاج فحص يدوي")
        
        # إبقاء المتصفح مفتوح للمراجعة اليدوية
        print(f"\n🔍 المتصفح مفتوح للمراجعة اليدوية...")
        print("   👀 تحقق من الصفحة الحالية بصرياً")
        print("   📝 لاحظ ما تراه في الصفحة")
        input("اضغط Enter لإغلاق المتصفح والخروج...")
        driver.quit()
        
        return login_status == "success"
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_multiple_working_accounts():
    """اختبار عدة حسابات تعمل"""
    print("🧪 اختبار عدة حسابات تعمل...")
    
    # حسابات يجب أن تعمل
    working_accounts = [
        ("<EMAIL>", "Hh1122334455"),
        # يمكنك إضافة المزيد من الحسابات التي تعرف أنها تعمل
    ]
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        results = []
        
        for i, (username, password) in enumerate(working_accounts, 1):
            print(f"\n{'='*60}")
            print(f"🔍 اختبار الحساب {i}: {username}")
            print(f"{'='*60}")
            
            try:
                # محو بيانات التصفح قبل كل اختبار
                if i > 1:
                    print("🧹 محو بيانات التصفح...")
                    clear_result = clear_browser_data(driver)
                    print(f"🗑️ نتيجة المحو: {clear_result}")
                
                # محاولة تسجيل الدخول
                login_status = login_shahid(driver, username, password)
                print(f"📊 نتيجة تسجيل الدخول: {login_status}")
                
                results.append({
                    'username': username,
                    'password': password,
                    'status': login_status,
                    'expected': 'success'
                })
                
                # انتظار قبل الحساب التالي
                time.sleep(3)
                
            except Exception as e:
                print(f"❌ خطأ في اختبار {username}: {e}")
                results.append({
                    'username': username,
                    'password': password,
                    'error': str(e)
                })
        
        print(f"\n{'='*60}")
        print("📊 ملخص نتائج الاختبار:")
        print(f"{'='*60}")
        
        correct_results = 0
        incorrect_results = 0
        
        for i, result in enumerate(results, 1):
            print(f"\n🔍 الحساب {i}: {result['username']}")
            if 'error' in result:
                print(f"   ❌ خطأ: {result['error']}")
                incorrect_results += 1
            else:
                print(f"   📊 النتيجة: {result['status']}")
                print(f"   🎯 المتوقع: {result['expected']}")
                if result['status'] == result['expected']:
                    print(f"   ✅ صحيح!")
                    correct_results += 1
                else:
                    print(f"   ❌ خطأ! النتيجة لا تطابق المتوقع")
                    incorrect_results += 1
        
        print(f"\n📈 النتائج النهائية:")
        print(f"   ✅ نتائج صحيحة: {correct_results}")
        print(f"   ❌ نتائج خاطئة: {incorrect_results}")
        
        if incorrect_results == 0:
            print("   🎉 ممتاز! جميع النتائج صحيحة!")
        else:
            print("   ⚠️ هناك مشاكل في اكتشاف النتائج")
        
        driver.quit()
        return incorrect_results == 0
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار حساب محدد")
    print("=" * 60)
    
    print("⚠️ هذا الاختبار مخصص لاختبار حساب محدد يعمل لكن يظهر كـ bad")
    print("🎯 الهدف: التأكد من أن البرنامج يتعرف على الحسابات الصحيحة")
    print()
    
    # اختبار الحساب المحدد
    print("1️⃣ اختبار الحساب المحدد...")
    if test_specific_account():
        print("✅ الحساب المحدد يعمل بشكل صحيح!")
    else:
        print("❌ هناك مشكلة في الحساب المحدد!")
    
    print("\n" + "="*60)
    response = input("هل تريد اختبار حسابات إضافية؟ (y/n): ").lower().strip()
    
    if response in ['y', 'yes', 'نعم', 'ن']:
        print("2️⃣ اختبار حسابات إضافية...")
        if test_multiple_working_accounts():
            print("✅ جميع الحسابات تعمل بشكل صحيح!")
        else:
            print("❌ هناك مشاكل في بعض الحسابات!")
    else:
        print("✅ تم تخطي اختبار الحسابات الإضافية")
    
    print("\n" + "="*60)
    print("🎉 انتهى الاختبار!")
    print("💡 إذا كان الحساب يعمل لكن يظهر كـ bad، فهناك مشكلة في منطق اكتشاف النجاح")
    print("="*60)

if __name__ == "__main__":
    main()
