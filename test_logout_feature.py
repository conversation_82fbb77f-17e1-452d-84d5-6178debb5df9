#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ميزة تسجيل الخروج الجديدة
Test the new logout feature
"""

import os
import sys
import time

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الدوال من الملف الرئيسي
from shahid import setup_chrome_driver, login_shahid, logout_shahid, check_login_status, force_logout_from_profiles

def test_logout_functionality():
    """اختبار وظائف تسجيل الخروج والدخول"""
    print("🧪 اختبار ميزة تسجيل الخروج الجديدة...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(driver_path)
        
        print("🚀 بدء تشغيل Chrome...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # اختبار بيانات وهمية
        test_accounts = [
            ("<EMAIL>", "password123"),
            ("<EMAIL>", "password456"),
            ("<EMAIL>", "password789")
        ]
        
        results = []
        
        print("\n" + "="*60)
        print("🔍 اختبار تسلسل تسجيل الدخول والخروج")
        print("="*60)
        
        for i, (username, password) in enumerate(test_accounts, 1):
            print(f"\n--- اختبار الحساب {i}: {username} ---")
            
            try:
                # فحص الحالة الحالية
                print("1️⃣ فحص الحالة الحالية...")
                initial_status = check_login_status(driver)
                print(f"   الحالة الأولية: {initial_status}")
                
                # إذا كان هناك حساب مسجل دخول، قم بتسجيل الخروج
                if i > 1:  # ابتداءً من الحساب الثاني
                    print("2️⃣ تسجيل الخروج من الحساب السابق...")
                    logout_status = logout_shahid(driver)
                    print(f"   حالة تسجيل الخروج: {logout_status}")
                    
                    # فحص الحالة بعد تسجيل الخروج
                    after_logout_status = check_login_status(driver)
                    print(f"   الحالة بعد تسجيل الخروج: {after_logout_status}")
                
                # محاولة تسجيل الدخول
                print("3️⃣ محاولة تسجيل الدخول...")
                login_status = login_shahid(driver, username, password)
                print(f"   حالة تسجيل الدخول: {login_status}")
                
                # فحص الحالة النهائية
                final_status = check_login_status(driver)
                print(f"   الحالة النهائية: {final_status}")
                
                results.append({
                    'account': username,
                    'initial_status': initial_status,
                    'logout_status': logout_status if i > 1 else 'N/A',
                    'login_status': login_status,
                    'final_status': final_status
                })
                
                # انتظار قصير بين المحاولات
                print("⏳ انتظار قبل الحساب التالي...")
                time.sleep(3)
                
            except Exception as e:
                print(f"❌ خطأ في اختبار {username}: {e}")
                results.append({
                    'account': username,
                    'error': str(e)
                })
        
        print("\n" + "="*60)
        print("📊 ملخص نتائج الاختبار:")
        print("="*60)
        
        for i, result in enumerate(results, 1):
            print(f"\n🔍 الحساب {i}: {result['account']}")
            if 'error' in result:
                print(f"   ❌ خطأ: {result['error']}")
            else:
                print(f"   📥 الحالة الأولية: {result['initial_status']}")
                print(f"   📤 تسجيل الخروج: {result['logout_status']}")
                print(f"   🔐 تسجيل الدخول: {result['login_status']}")
                print(f"   📊 الحالة النهائية: {result['final_status']}")
        
        print("\n" + "="*60)
        print("🔍 تحليل النتائج:")
        print("="*60)
        
        # تحليل النتائج
        successful_logins = sum(1 for r in results if r.get('login_status') not in ['login_form_not_found', 'error'])
        successful_logouts = sum(1 for r in results if r.get('logout_status') in ['success', 'already_logged_out', 'forced_logout'])
        
        print(f"✅ محاولات تسجيل دخول ناجحة: {successful_logins}/{len(results)}")
        print(f"✅ محاولات تسجيل خروج ناجحة: {successful_logouts}/{len(results)-1}")
        
        # فحص إذا كانت المشكلة الأصلية محلولة
        login_form_not_found_count = sum(1 for r in results if r.get('login_status') == 'login_form_not_found')
        if login_form_not_found_count == 0:
            print("🎉 ممتاز! لم تعد هناك مشكلة 'login_form_not_found'")
        else:
            print(f"⚠️ لا تزال هناك {login_form_not_found_count} حالة 'login_form_not_found'")
        
        # إبقاء المتصفح مفتوح للمراجعة اليدوية
        print("\n🔍 المتصفح مفتوح للمراجعة اليدوية...")
        input("اضغط Enter لإغلاق المتصفح والخروج...")
        driver.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_status_check_only():
    """اختبار فحص الحالة فقط بدون تسجيل دخول"""
    print("🔍 اختبار فحص حالة تسجيل الدخول...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--start-maximized")
        
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # اختبار صفحات مختلفة
        test_urls = [
            "https://shahid.mbc.net",
            "https://shahid.mbc.net/ar/login",
            "https://shahid.mbc.net/ar/signin"
        ]
        
        for url in test_urls:
            print(f"\n🌐 اختبار: {url}")
            driver.get(url)
            time.sleep(3)
            
            status = check_login_status(driver)
            print(f"   📊 الحالة: {status}")
            print(f"   🔗 الرابط الحالي: {driver.current_url}")
            print(f"   📄 عنوان الصفحة: {driver.title}")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار فحص الحالة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار ميزة تسجيل الخروج الجديدة")
    print("=" * 60)
    
    print("⚠️ هذا الاختبار سيفتح المتصفح ويختبر الوظائف الجديدة")
    print("⚠️ لن يتم تسجيل دخول حقيقي، فقط اختبار الآليات")
    print()
    
    # اختبار فحص الحالة أولاً
    print("1️⃣ اختبار فحص حالة تسجيل الدخول...")
    if test_status_check_only():
        print("✅ اختبار فحص الحالة نجح!")
    else:
        print("❌ اختبار فحص الحالة فشل!")
        return
    
    print("\n" + "="*60)
    response = input("هل تريد اختبار تسلسل تسجيل الدخول والخروج؟ (y/n): ").lower().strip()
    
    if response in ['y', 'yes', 'نعم', 'ن']:
        print("2️⃣ اختبار تسلسل تسجيل الدخول والخروج...")
        if test_logout_functionality():
            print("✅ اختبار التسلسل نجح!")
        else:
            print("❌ اختبار التسلسل فشل!")
    else:
        print("✅ تم تخطي اختبار التسلسل")
    
    print("\n" + "="*60)
    print("🎉 انتهى الاختبار!")
    print("💡 إذا نجحت الاختبارات، يمكنك الآن استخدام الأداة الرئيسية")
    print("="*60)

if __name__ == "__main__":
    main()
