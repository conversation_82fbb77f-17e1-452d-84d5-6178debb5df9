# أداة فحص حسابات Shahid

أداة Python لفحص صحة حسابات موقع Shahid (شاهد) التابع لـ MBC بشكل تلقائي.

## الميزات

- ✅ **تحميل ChromeDriver تلقائياً** باستخدام Chrome for Testing API الجديد
- ✅ **دعم أنظمة تشغيل متعددة** (Windows, macOS, Linux)
- ✅ **حل الكابتشا النصية** باستخدام OCR
- ✅ **دعم reCAPTCHA** (يطلب التدخل اليدوي)
- ✅ **واجهة رسومية بسيطة** لاختيار ملف الحسابات
- ✅ **حفظ النتائج** في ملفات منفصلة (good.txt و bad.txt)
- ✅ **التحقق من توافق ChromeDriver** مع إصدار Chrome المثبت
- 🆕 **تسجيل خروج تلقائي** بين الحسابات لتجنب مشكلة "login_form_not_found"
- 🆕 **فحص حالة تسجيل الدخول** قبل كل محاولة
- 🆕 **معالجة محسنة للأخطاء** مع رسائل واضحة

## المتطلبات

### 1. Python 3.7 أو أحدث
### 2. Google Chrome متصفح
### 3. Tesseract OCR

#### تثبيت Tesseract OCR:

**Windows:**
- حمل من: https://github.com/UB-Mannheim/tesseract/wiki
- ثبت في المسار الافتراضي: `C:\Program Files\Tesseract-OCR\`

**Ubuntu/Debian:**
```bash
sudo apt-get install tesseract-ocr
```

**macOS:**
```bash
brew install tesseract
```

## التثبيت

1. **استنسخ أو حمل الملفات:**
```bash
git clone <repository-url>
cd shahid-checker
```

2. **ثبت المتطلبات:**
```bash
pip install -r requirements.txt
```

3. **اختبر الإعداد:**
```bash
# على Windows
run_test.bat

# أو باستخدام Python مباشرة
python test_setup.py
```

4. **اختبر الميزات الجديدة (اختياري):**
```bash
# اختبار ميزة تسجيل الخروج التلقائي
test_logout.bat

# اختبار التعامل مع صفحة الملفات الشخصية
test_profiles.bat

# اختبار محو بيانات التصفح
test_clear_data.bat

# اختبار التحقق الدقيق من النتائج
test_validation.bat

# اختبار حساب محدد (للتشخيص)
test_specific_account.bat

# أو باستخدام Python مباشرة
python test_logout_feature.py
python test_profiles_logout.py
python test_clear_data.py
python test_validation.py
python test_specific_account.py
```

## الاستخدام

1. **تحضير ملف الحسابات:**
   - أنشئ ملف نصي (.txt) يحتوي على الحسابات
   - كل سطر يجب أن يكون بالصيغة: `email:password`
   - مثال:
     ```
     <EMAIL>:password123
     <EMAIL>:mypassword
     ```

2. **تشغيل الأداة:**
```bash
python shahid.py
```

3. **اختيار الملف:**
   - ستظهر نافذة لاختيار ملف الحسابات
   - اختر الملف النصي الذي يحتوي على الحسابات

4. **انتظار النتائج:**
   - ستبدأ الأداة في فحص الحسابات تلقائياً
   - إذا ظهرت reCAPTCHA، ستحتاج لحلها يدوياً
   - النتائج ستُحفظ في:
     - `good.txt`: الحسابات الصالحة
     - `bad.txt`: الحسابات غير الصالحة مع سبب الفشل

## إدارة ChromeDriver

الأداة تدعم عدة طرق لإدارة ChromeDriver:

1. **التحميل التلقائي** (الطريقة المفضلة)
2. **webdriver-manager** (إذا كان مثبتاً)
3. **النسخ من النظام** (إذا كان ChromeDriver في PATH)
4. **التحميل اليدوي** كحل أخير

### إذا واجهت مشاكل مع ChromeDriver:

1. **ثبت webdriver-manager:**
```bash
pip install webdriver-manager
```

2. **أو حمل ChromeDriver يدوياً:**
   - اذهب إلى: https://googlechromelabs.github.io/chrome-for-testing/
   - حمل الإصدار المتوافق مع Chrome
   - ضع الملف في نفس مجلد السكريبت

## استكشاف الأخطاء

### مشكلة Tesseract:
```
TesseractNotFoundError
```
**الحل:** تأكد من تثبيت Tesseract وتحديث المسار في السطر 20 من الكود.

### مشكلة ChromeDriver:
```
فشل في إعداد ChromeDriver
```
**الحل:** 
- تأكد من تثبيت Chrome
- جرب تثبيت webdriver-manager
- أو حمل ChromeDriver يدوياً

### مشكلة الشبكة:
```
خطأ في الشبكة أثناء تحميل ChromeDriver
```
**الحل:** تحقق من اتصال الإنترنت أو استخدم VPN.

## التحديثات الجديدة (v2.0)

### 🆕 ميزة محو بيانات التصفح التلقائي
- **المشكلة المحلولة:** عندما كان البرنامج ينتقل من حساب لآخر، كان يبقى مسجل دخول ولا يجد صفحة تسجيل الدخول للحساب الجديد
- **المشكلة الخاصة:** الوصول إلى صفحة الملفات الشخصية (اختر من يشاهد الآن) بدلاً من صفحة تسجيل الدخول
- **الحل الجديد:** محو بيانات التصفح بالكامل بدلاً من تسجيل الخروج التقليدي
- **ما يتم محوه:**
  - ملفات تعريف الارتباط (Cookies)
  - Local Storage
  - Session Storage
  - Cache
- **النتيجة:** حل نهائي وموثوق لمشكلة "login_form_not_found" و "stuck_in_profiles_page"

### 🆕 ميزة الحفظ التلقائي والتحقق الدقيق
- **الحفظ التلقائي:** حفظ النتائج بعد كل حساب لتجنب فقدان البيانات
- **التحقق الدقيق:** فحص رسائل الخطأ أولاً قبل فحص علامات النجاح
- **رسائل الخطأ المدعومة:**
  - "اسم مستخدم أو كلمة سر غير صحيحة"
  - "بيانات الدخول غير صحيحة"
  - "البيانات المدخلة غير صحيحة"
- **اكتشاف ذكي للنجاح:** التعرف على صفحة الملفات الشخصية كعلامة نجاح
- **تأكيد يدوي:** للحالات الغامضة، يطلب البرنامج تأكيد من المستخدم
- **النتيجة:** تقليل النتائج الإيجابية الخاطئة وحماية البيانات

### 🔧 تحسينات أخرى
- فحص حالة تسجيل الدخول قبل كل محاولة
- اكتشاف صفحة الملفات الشخصية تلقائياً
- محو بيانات التصفح بالكامل (Cookies, localStorage, sessionStorage, Cache)
- محو البيانات بين كل حساب لضمان بداية نظيفة
- حفظ تلقائي للنتائج بعد كل حساب
- ملفات مؤقتة لحماية البيانات من الفقدان
- فحص رسائل الخطأ أولاً لتجنب النتائج الخاطئة
- معالجة أفضل للأخطاء مع رسائل واضحة
- إضافة اختبارات شاملة للميزات الجديدة

## ملاحظات مهمة

- ⚠️ **استخدم الأداة بمسؤولية** واحترم شروط الخدمة
- ⚠️ **لا تفرط في الاستخدام** لتجنب حظر IP
- ⚠️ **احم بياناتك** ولا تشارك ملفات الحسابات
- ⚠️ **استخدم VPN** إذا لزم الأمر

## الدعم

إذا واجهت أي مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. تحقق من إصدارات Python و Chrome
3. جرب تشغيل الأداة كمدير (Administrator)

## الترخيص

هذه الأداة للأغراض التعليمية فقط. استخدمها بمسؤولية.
