#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التعامل مع صفحة الملفات الشخصية
Test handling of profiles page
"""

import os
import sys
import time

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الدوال من الملف الرئيسي
from shahid import setup_chrome_driver, login_shahid, logout_shahid, check_login_status, clear_browser_data

def test_profiles_page_detection():
    """اختبار اكتشاف صفحة الملفات الشخصية"""
    print("🧪 اختبار اكتشاف صفحة الملفات الشخصية...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(driver_path)
        
        print("🚀 بدء تشغيل Chrome...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # اختبار صفحات مختلفة
        test_urls = [
            "https://shahid.mbc.net",
            "https://shahid.mbc.net/ar/login",
            "https://shahid.mbc.net/ar/profiles"  # إذا كان هذا الرابط موجود
        ]
        
        results = []
        
        for url in test_urls:
            print(f"\n🌐 اختبار: {url}")
            try:
                driver.get(url)
                time.sleep(5)  # انتظار أطول لتحميل الصفحة
                
                status = check_login_status(driver)
                current_url = driver.current_url
                page_title = driver.title
                
                print(f"   📊 الحالة: {status}")
                print(f"   🔗 الرابط الحالي: {current_url}")
                print(f"   📄 عنوان الصفحة: {page_title}")
                
                # فحص محتوى الصفحة
                page_source = driver.page_source.lower()
                profiles_indicators = [
                    "مرحباً اختر من يشاهد" in page_source,
                    "اختر من يشاهد" in page_source,
                    "أطفال" in page_source,
                    "kids" in page_source,
                    "إدارة الملفات" in page_source,
                    "profile" in current_url.lower()
                ]
                
                if any(profiles_indicators):
                    print("   🎭 تم اكتشاف صفحة الملفات الشخصية!")
                    
                    # اختبار دالة محو بيانات التصفح
                    print("   🧹 اختبار محو بيانات التصفح...")
                    clear_result = clear_browser_data(driver)
                    print(f"   🗑️ نتيجة محو بيانات التصفح: {clear_result}")

                    # فحص الحالة بعد محو البيانات
                    new_status = check_login_status(driver)
                    print(f"   📊 الحالة بعد محو البيانات: {new_status}")

                    results.append({
                        'url': url,
                        'initial_status': status,
                        'profiles_detected': True,
                        'clear_data_result': clear_result,
                        'final_status': new_status
                    })
                else:
                    print("   ℹ️ لم يتم اكتشاف صفحة الملفات الشخصية")
                    results.append({
                        'url': url,
                        'initial_status': status,
                        'profiles_detected': False
                    })
                
            except Exception as e:
                print(f"   ❌ خطأ في اختبار {url}: {e}")
                results.append({
                    'url': url,
                    'error': str(e)
                })
        
        print("\n" + "="*60)
        print("📊 ملخص نتائج الاختبار:")
        print("="*60)
        
        for result in results:
            print(f"\n🔍 {result['url']}")
            if 'error' in result:
                print(f"   ❌ خطأ: {result['error']}")
            else:
                print(f"   📊 الحالة الأولية: {result['initial_status']}")
                print(f"   🎭 صفحة ملفات شخصية: {'نعم' if result['profiles_detected'] else 'لا'}")
                if result['profiles_detected']:
                    print(f"   🗑️ محو بيانات التصفح: {result.get('clear_data_result', 'N/A')}")
                    print(f"   📊 الحالة النهائية: {result.get('final_status', 'N/A')}")
        
        # إبقاء المتصفح مفتوح للمراجعة اليدوية
        print("\n🔍 المتصفح مفتوح للمراجعة اليدوية...")
        input("اضغط Enter لإغلاق المتصفح والخروج...")
        driver.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_multiple_accounts_with_profiles():
    """اختبار حسابات متعددة مع التعامل مع صفحة الملفات الشخصية"""
    print("🧪 اختبار حسابات متعددة مع صفحة الملفات الشخصية...")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # حسابات اختبار
        test_accounts = [
            ("<EMAIL>", "password123"),
            ("<EMAIL>", "password456"),
            ("<EMAIL>", "password789")
        ]
        
        results = []
        
        for i, (username, password) in enumerate(test_accounts, 1):
            print(f"\n{'='*50}")
            print(f"🔍 اختبار الحساب {i}: {username}")
            print(f"{'='*50}")
            
            try:
                # فحص الحالة الأولية
                initial_status = check_login_status(driver)
                print(f"1️⃣ الحالة الأولية: {initial_status}")
                
                # إذا كان هناك حساب مسجل دخول، قم بتسجيل الخروج
                if i > 1:
                    print("2️⃣ تسجيل الخروج من الحساب السابق...")
                    logout_status = logout_shahid(driver)
                    print(f"   حالة تسجيل الخروج: {logout_status}")
                    
                    # فحص الحالة بعد تسجيل الخروج
                    after_logout_status = check_login_status(driver)
                    print(f"   الحالة بعد تسجيل الخروج: {after_logout_status}")
                
                # محاولة تسجيل الدخول
                print("3️⃣ محاولة تسجيل الدخول...")
                login_status = login_shahid(driver, username, password)
                print(f"   حالة تسجيل الدخول: {login_status}")
                
                # فحص الحالة النهائية
                final_status = check_login_status(driver)
                print(f"4️⃣ الحالة النهائية: {final_status}")
                
                results.append({
                    'account': username,
                    'initial_status': initial_status,
                    'logout_status': logout_status if i > 1 else 'N/A',
                    'login_status': login_status,
                    'final_status': final_status
                })
                
                # انتظار قبل الحساب التالي
                print("⏳ انتظار قبل الحساب التالي...")
                time.sleep(3)
                
            except Exception as e:
                print(f"❌ خطأ في اختبار {username}: {e}")
                results.append({
                    'account': username,
                    'error': str(e)
                })
        
        print("\n" + "="*60)
        print("📊 ملخص نتائج الاختبار:")
        print("="*60)
        
        for i, result in enumerate(results, 1):
            print(f"\n🔍 الحساب {i}: {result['account']}")
            if 'error' in result:
                print(f"   ❌ خطأ: {result['error']}")
            else:
                print(f"   📥 الحالة الأولية: {result['initial_status']}")
                print(f"   📤 تسجيل الخروج: {result['logout_status']}")
                print(f"   🔐 تسجيل الدخول: {result['login_status']}")
                print(f"   📊 الحالة النهائية: {result['final_status']}")
        
        # تحليل النتائج
        login_form_not_found_count = sum(1 for r in results if r.get('login_status') == 'login_form_not_found')
        stuck_in_profiles_count = sum(1 for r in results if r.get('login_status') == 'stuck_in_profiles_page')
        
        print(f"\n📈 تحليل النتائج:")
        print(f"   ❌ حالات login_form_not_found: {login_form_not_found_count}")
        print(f"   🎭 حالات stuck_in_profiles_page: {stuck_in_profiles_count}")
        
        if login_form_not_found_count == 0 and stuck_in_profiles_count == 0:
            print("   🎉 ممتاز! تم حل مشكلة صفحة الملفات الشخصية!")
        else:
            print("   ⚠️ لا تزال هناك مشاكل تحتاج لمعالجة")
        
        # إبقاء المتصفح مفتوح للمراجعة اليدوية
        print("\n🔍 المتصفح مفتوح للمراجعة اليدوية...")
        input("اضغط Enter لإغلاق المتصفح والخروج...")
        driver.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار التعامل مع صفحة الملفات الشخصية")
    print("=" * 60)
    
    print("⚠️ هذا الاختبار مخصص لحل مشكلة صفحة الملفات الشخصية")
    print("⚠️ سيتم اختبار اكتشاف الصفحة وتسجيل الخروج القسري")
    print()
    
    # اختبار اكتشاف صفحة الملفات الشخصية
    print("1️⃣ اختبار اكتشاف صفحة الملفات الشخصية...")
    if test_profiles_page_detection():
        print("✅ اختبار اكتشاف الصفحة نجح!")
    else:
        print("❌ اختبار اكتشاف الصفحة فشل!")
        return
    
    print("\n" + "="*60)
    response = input("هل تريد اختبار حسابات متعددة؟ (y/n): ").lower().strip()
    
    if response in ['y', 'yes', 'نعم', 'ن']:
        print("2️⃣ اختبار حسابات متعددة...")
        if test_multiple_accounts_with_profiles():
            print("✅ اختبار الحسابات المتعددة نجح!")
        else:
            print("❌ اختبار الحسابات المتعددة فشل!")
    else:
        print("✅ تم تخطي اختبار الحسابات المتعددة")
    
    print("\n" + "="*60)
    print("🎉 انتهى الاختبار!")
    print("💡 إذا نجحت الاختبارات، فقد تم حل مشكلة صفحة الملفات الشخصية")
    print("="*60)

if __name__ == "__main__":
    main()
