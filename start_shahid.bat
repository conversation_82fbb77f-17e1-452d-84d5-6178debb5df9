@echo off
chcp 65001 >nul
title Shahid Checker - Simple Start

echo ========================================
echo    Shahid Account Checker
echo ========================================
echo.

REM تغيير المجلد إلى مجلد السكريبت
cd /d "%~dp0"

echo 📁 المجلد الحالي: %CD%
echo.

REM جرب تشغيل البرنامج بطرق مختلفة
echo 🚀 بدء تشغيل Shahid Checker...
echo.

REM الطريقة الأولى: python
python shahid.py
if not errorlevel 1 goto success

REM الطريقة الثانية: py
echo جاري المحاولة مع py...
py shahid.py
if not errorlevel 1 goto success

REM الطريقة الثالثة: python3
echo جاري المحاولة مع python3...
python3 shahid.py
if not errorlevel 1 goto success

REM إذا فشل كل شيء
echo.
echo ❌ فشل في تشغيل البرنامج!
echo.
echo الحلول المقترحة:
echo 1. تأكد من تثبيت Python
echo 2. أضف Python إلى PATH
echo 3. شغل الأمر يدوياً: python shahid.py
echo.
pause
exit /b 1

:success
echo.
echo 🏁 انتهى تشغيل البرنامج
pause
