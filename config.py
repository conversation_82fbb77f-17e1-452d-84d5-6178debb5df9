#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التكوين لأداة فحص حسابات Shahid
Configuration file for Shahid account checker
"""

import os
import platform

# =============================================================================
# إعدادات Tesseract OCR
# Tesseract OCR Settings
# =============================================================================

# مسار Tesseract حسب نظام التشغيل
# Tesseract path based on operating system
if platform.system() == "Windows":
    # Windows - المسار الافتراضي
    TESSERACT_PATH = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
    
    # مسارات بديلة للبحث
    TESSERACT_ALTERNATIVE_PATHS = [
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Users\<USER>\AppData\Local\Tesseract-OCR\tesseract.exe".format(os.getenv('USERNAME', '')),
        r"C:\tesseract\tesseract.exe"
    ]
    
elif platform.system() == "Darwin":  # macOS
    TESSERACT_PATH = "/usr/local/bin/tesseract"
    TESSERACT_ALTERNATIVE_PATHS = [
        "/opt/homebrew/bin/tesseract",
        "/usr/bin/tesseract"
    ]
    
else:  # Linux
    TESSERACT_PATH = "/usr/bin/tesseract"
    TESSERACT_ALTERNATIVE_PATHS = [
        "/usr/local/bin/tesseract",
        "/opt/tesseract/bin/tesseract"
    ]

# =============================================================================
# إعدادات ChromeDriver
# ChromeDriver Settings  
# =============================================================================

# تفضيل طريقة تحميل ChromeDriver
# ChromeDriver download preference
CHROMEDRIVER_AUTO_DOWNLOAD = True  # تحميل تلقائي
USE_WEBDRIVER_MANAGER = True       # استخدام webdriver-manager إذا كان متاح

# =============================================================================
# إعدادات Selenium
# Selenium Settings
# =============================================================================

# خيارات Chrome
CHROME_OPTIONS = [
    "--disable-blink-features=AutomationControlled",
    "--disable-dev-shm-usage",
    "--no-sandbox",
    "--start-maximized",
    # إعدادات خاصة لـ RDP وبيئات الخادم
    "--disable-gpu",                    # تعطيل GPU (مهم للـ RDP)
    "--disable-gpu-sandbox",            # تعطيل GPU sandbox
    "--disable-software-rasterizer",    # تعطيل software rasterizer
    "--disable-background-timer-throttling",
    "--disable-backgrounding-occluded-windows",
    "--disable-renderer-backgrounding",
    "--disable-features=TranslateUI",
    "--disable-ipc-flooding-protection",
    "--disable-web-security",           # تعطيل web security للمواقع المعقدة
    "--disable-features=VizDisplayCompositor",
    "--disable-extensions",             # تعطيل الإضافات
    "--disable-plugins",                # تعطيل البرمجيات المساعدة
    "--no-first-run",                   # تخطي الإعداد الأولي
    "--no-default-browser-check",       # تخطي فحص المتصفح الافتراضي
    "--disable-default-apps",           # تعطيل التطبيقات الافتراضية
    "--disable-popup-blocking",         # السماح بالنوافذ المنبثقة
    "--disable-translate",              # تعطيل الترجمة
    "--disable-background-networking",  # تعطيل الشبكة في الخلفية
    "--disable-sync",                   # تعطيل المزامنة
    "--disable-speech-api",             # تعطيل API الصوت
    "--disable-audio-output",           # تعطيل الصوت
    "--mute-audio",                     # كتم الصوت
    "--disable-logging",                # تقليل الرسائل
    "--log-level=3"                     # تقليل مستوى السجلات
]

# خيارات تجريبية
CHROME_EXPERIMENTAL_OPTIONS = {
    "excludeSwitches": ["enable-automation"],
    "useAutomationExtension": False
}

# =============================================================================
# إعدادات OCR
# OCR Settings
# =============================================================================

# إعدادات Tesseract للكابتشا
TESSERACT_CONFIG = '--psm 8'  # Page Segmentation Mode

# =============================================================================
# إعدادات عامة
# General Settings
# =============================================================================

# أوقات الانتظار (بالثواني)
WAIT_TIMES = {
    'page_load': 3,
    'element_wait': 1,
    'after_login': 5,
    'captcha_solve': 5,
    'recaptcha_manual': 15,
    'between_accounts': 2,
    'logout_wait': 3  # وقت انتظار بعد تسجيل الخروج
}

# أسماء ملفات النتائج
OUTPUT_FILES = {
    'good_accounts': 'good.txt',
    'bad_accounts': 'bad.txt'
}

# =============================================================================
# دوال مساعدة
# Helper Functions
# =============================================================================

def get_tesseract_path():
    """الحصول على مسار Tesseract الصحيح"""
    # جرب المسار الرئيسي أولاً
    if os.path.exists(TESSERACT_PATH):
        return TESSERACT_PATH
    
    # جرب المسارات البديلة
    for path in TESSERACT_ALTERNATIVE_PATHS:
        if os.path.exists(path):
            return path
    
    # جرب البحث في PATH
    import shutil
    system_tesseract = shutil.which("tesseract")
    if system_tesseract:
        return system_tesseract
    
    # إرجاع المسار الافتراضي حتى لو لم يكن موجود
    return TESSERACT_PATH

def validate_config():
    """التحقق من صحة التكوين"""
    issues = []
    
    # تحقق من Tesseract
    tesseract_path = get_tesseract_path()
    if not os.path.exists(tesseract_path):
        issues.append(f"Tesseract غير موجود في: {tesseract_path}")
    
    return issues

# =============================================================================
# تطبيق التكوين
# Apply Configuration
# =============================================================================

def apply_tesseract_config():
    """تطبيق إعدادات Tesseract"""
    try:
        import pytesseract
        tesseract_path = get_tesseract_path()
        pytesseract.pytesseract.tesseract_cmd = tesseract_path
        return True, f"تم تعيين مسار Tesseract: {tesseract_path}"
    except ImportError:
        return False, "مكتبة pytesseract غير مثبتة"
    except Exception as e:
        return False, f"خطأ في تطبيق إعدادات Tesseract: {e}"

if __name__ == "__main__":
    # اختبار التكوين
    print("🔧 اختبار ملف التكوين...")
    
    print(f"نظام التشغيل: {platform.system()}")
    print(f"مسار Tesseract: {get_tesseract_path()}")
    
    issues = validate_config()
    if issues:
        print("⚠️  مشاكل في التكوين:")
        for issue in issues:
            print(f"  - {issue}")
    else:
        print("✅ التكوين صحيح!")
    
    success, message = apply_tesseract_config()
    if success:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")
