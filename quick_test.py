#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للتأكد من أن الكود يعمل
Quick test to ensure the code works
"""

def test_imports():
    """اختبار استيراد الوحدات"""
    try:
        print("🔍 اختبار استيراد الوحدات...")
        
        # اختبار استيراد الوحدات الأساسية
        import os
        import time
        import sys
        print("✅ تم استيراد الوحدات الأساسية")
        
        # اختبار استيراد selenium
        from selenium import webdriver
        from selenium.webdriver.common.by import By
        print("✅ تم استيراد selenium")
        
        # اختبار استيراد PIL
        from PIL import Image
        print("✅ تم استيراد PIL")
        
        # اختبار استيراد pytesseract
        import pytesseract
        print("✅ تم استيراد pytesseract")
        
        # اختبار استيراد tkinter
        import tkinter as tk
        print("✅ تم استيراد tkinter")
        
        # اختبار استيراد requests
        import requests
        print("✅ تم استيراد requests")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def test_config():
    """اختبار ملف التكوين"""
    try:
        print("\n🔧 اختبار ملف التكوين...")
        
        from config import WAIT_TIMES, OUTPUT_FILES, CHROME_OPTIONS
        print("✅ تم استيراد إعدادات التكوين")
        
        # تحقق من وجود الإعدادات الجديدة
        if 'logout_wait' in WAIT_TIMES:
            print("✅ تم العثور على إعداد logout_wait الجديد")
        else:
            print("⚠️ لم يتم العثور على إعداد logout_wait")
        
        print(f"📊 أوقات الانتظار: {WAIT_TIMES}")
        print(f"📁 ملفات النتائج: {OUTPUT_FILES}")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد ملف التكوين: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في ملف التكوين: {e}")
        return False

def test_shahid_functions():
    """اختبار دوال shahid"""
    try:
        print("\n🧪 اختبار دوال shahid...")
        
        # اختبار استيراد الدوال
        from shahid import check_login_status, logout_shahid, login_shahid
        print("✅ تم استيراد الدوال الجديدة")
        
        # اختبار استيراد الدوال الأخرى
        from shahid import setup_chrome_driver, solve_captcha_with_ocr
        print("✅ تم استيراد الدوال الأساسية")
        
        return True
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد دوال shahid: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ في دوال shahid: {e}")
        return False

def test_syntax():
    """اختبار بناء الجملة"""
    try:
        print("\n📝 اختبار بناء الجملة...")
        
        import py_compile
        py_compile.compile('shahid.py', doraise=True)
        print("✅ بناء الجملة صحيح في shahid.py")
        
        py_compile.compile('config.py', doraise=True)
        print("✅ بناء الجملة صحيح في config.py")
        
        return True
        
    except py_compile.PyCompileError as e:
        print(f"❌ خطأ في بناء الجملة: {e}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار سريع للتحديثات الجديدة")
    print("=" * 60)
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("ملف التكوين", test_config),
        ("دوال shahid", test_shahid_functions),
        ("بناء الجملة", test_syntax)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*40}")
        print(f"🔍 اختبار: {test_name}")
        print(f"{'='*40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ نجح اختبار: {test_name}")
            else:
                print(f"❌ فشل اختبار: {test_name}")
                
        except Exception as e:
            print(f"💥 خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # ملخص النتائج
    print(f"\n{'='*60}")
    print("📊 ملخص نتائج الاختبار:")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📈 النتيجة النهائية: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! يمكنك الآن استخدام الأداة.")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
