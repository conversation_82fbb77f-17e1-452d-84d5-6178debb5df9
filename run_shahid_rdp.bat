@echo off
chcp 65001 >nul
title Shahid Checker - RDP Optimized

echo ========================================
echo    Shahid Account Checker - RDP Mode
echo ========================================
echo.

REM تعيين متغيرات البيئة لتحسين الأداء في RDP
set CHROME_LOG_FILE=NUL
set CHROME_DEVEL_SANDBOX=0
set DISPLAY_SCALE=100
set GPU_FORCE_SOFTWARE_RENDERING=1

REM تعطيل رسائل الخطأ غير المهمة
set PYTHONWARNINGS=ignore
set SELENIUM_LOG_LEVEL=WARNING

echo 🔧 تم تعيين إعدادات RDP المحسنة...
echo.

REM التحقق من وجود Python
where python >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python أولاً من: https://www.python.org/downloads/
    echo أو استخدم: py shahid.py
    pause
    exit /b 1
)

REM التحقق من وجود الملفات المطلوبة
if not exist "shahid.py" (
    echo ❌ ملف shahid.py غير موجود
    pause
    exit /b 1
)

if not exist "config.py" (
    echo ❌ ملف config.py غير موجود
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة
echo.

REM تثبيت المتطلبات إذا لزم الأمر
echo 📦 التحقق من المتطلبات...
where pip >nul 2>&1
if not errorlevel 1 (
    pip install -r requirements.txt --quiet --disable-pip-version-check 2>nul
) else (
    echo ⚠️ pip غير متوفر، تخطي تثبيت المتطلبات...
)

echo.
echo 🚀 بدء تشغيل Shahid Checker في وضع RDP...
echo.
echo ملاحظات مهمة لبيئة RDP:
echo - تم تعطيل GPU لتجنب الأخطاء
echo - تم تقليل رسائل السجل
echo - تم تحسين استهلاك الذاكرة
echo - قد يكون الأداء أبطأ قليلاً من البيئة العادية
echo.

REM تشغيل البرنامج - جرب طرق مختلفة
echo جاري تشغيل البرنامج...
python shahid.py 2>nul
if errorlevel 1 (
    echo جاري المحاولة مع py...
    py shahid.py 2>nul
    if errorlevel 1 (
        echo جاري المحاولة مع python3...
        python3 shahid.py 2>nul
        if errorlevel 1 (
            echo ❌ فشل في تشغيل البرنامج
            echo جرب تشغيل: python shahid.py يدوياً
            pause
            exit /b 1
        )
    )
)

echo.
echo 🏁 انتهى تشغيل البرنامج
pause
