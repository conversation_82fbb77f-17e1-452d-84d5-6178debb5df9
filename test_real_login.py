#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تسجيل الدخول الحقيقي لـ Shahid
Real login test for Shahid
"""

import os
import sys
import time

# إضافة المجلد الحالي إلى المسار
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الدوال من الملف الرئيسي
from shahid import setup_chrome_driver, login_shahid

def test_real_login():
    """اختبار تسجيل الدخول الحقيقي"""
    print("🔍 اختبار تسجيل الدخول الحقيقي لـ Shahid...")
    print("⚠️  سيتم استخدام بيانات حقيقية للاختبار")
    
    # قراءة بيانات الاختبار
    test_file = "real_test_accounts.txt"
    if not os.path.exists(test_file):
        print(f"❌ ملف الاختبار غير موجود: {test_file}")
        return False
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        
        # إعداد ChromeDriver
        driver_path = setup_chrome_driver()
        if not driver_path:
            print("❌ فشل في إعداد ChromeDriver")
            return False
        
        # إعداد خيارات Chrome (مع واجهة مرئية للمراقبة)
        chrome_options = Options()
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        service = Service(driver_path)
        
        print("🚀 بدء تشغيل Chrome...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # قراءة الحسابات
        with open(test_file, encoding="utf-8", errors="ignore") as f:
            lines = [l.strip() for l in f if l.strip()]
        
        print(f"📄 تم قراءة {len(lines)} حساب للاختبار")
        
        results = []
        
        for i, line in enumerate(lines, 1):
            if ":" not in line:
                print(f"❌ [{i}] خطأ في التنسيق: {line}")
                results.append((line, "format_error"))
                continue
            
            try:
                username, password = line.split(":", 1)
                print(f"\n{'='*60}")
                print(f"🔍 [{i}/{len(lines)}] اختبار الحساب: {username}")
                print(f"{'='*60}")
                
                status = login_shahid(driver, username, password)
                
                print(f"\n📊 النتيجة النهائية: {status}")
                results.append((username, status))
                
                # انتظار بين المحاولات
                if i < len(lines):
                    print("⏳ انتظار قبل المحاولة التالية...")
                    time.sleep(5)
                
            except Exception as e:
                print(f"❌ خطأ في اختبار {line}: {e}")
                results.append((line, "error"))
        
        print(f"\n{'='*60}")
        print("📊 ملخص نتائج الاختبار:")
        print(f"{'='*60}")
        
        for account, status in results:
            status_emoji = {
                "success": "✅",
                "fail": "❌", 
                "unknown": "❓",
                "still_on_login_page": "⚠️",
                "login_form_not_found": "🔍",
                "error": "💥",
                "format_error": "📝"
            }.get(status, "❓")
            
            print(f"{status_emoji} {account}: {status}")
        
        print(f"{'='*60}")
        
        # إبقاء المتصفح مفتوح للمراجعة اليدوية
        input("\n🔍 اضغط Enter لإغلاق المتصفح والخروج...")
        driver.quit()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار تسجيل الدخول الحقيقي لـ Shahid")
    print("=" * 60)
    
    print("⚠️  تحذير: هذا الاختبار سيستخدم بيانات حقيقية")
    print("⚠️  تأكد من أن البيانات في ملف real_test_accounts.txt صحيحة")
    print()
    
    response = input("هل تريد المتابعة؟ (y/n): ").lower().strip()
    
    if response in ['y', 'yes', 'نعم', 'ن']:
        test_real_login()
    else:
        print("✅ تم إلغاء الاختبار")
    
    print("\n" + "="*60)
    print("🎉 انتهى الاختبار!")
    print("="*60)

if __name__ == "__main__":
    main()
