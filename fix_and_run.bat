@echo off
chcp 65001 >nul
title Shahid Checker - Fix and Run

echo ========================================
echo    Shahid Checker - إصلاح وتشغيل
echo ========================================
echo.

cd /d "%~dp0"

echo 🔧 إصلاح مشاكل ChromeDriver...
echo.

REM حذف ChromeDriver المعطوب إن وجد
if exist "chromedriver.exe" (
    echo 🗑️ حذف ChromeDriver المعطوب...
    del /f /q "chromedriver.exe" 2>nul
)

REM تثبيت webdriver-manager إذا لم يكن مثبت
echo 📦 التأكد من تثبيت webdriver-manager...
python -m pip install webdriver-manager --quiet --disable-pip-version-check 2>nul

echo.
echo 🚀 تشغيل البرنامج مع إصلاح تلقائي...
echo.

REM تشغيل البرنامج
python shahid.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ أثناء التشغيل
    echo.
    echo الحلول المقترحة:
    echo 1. أعد تشغيل الكمبيوتر
    echo 2. حدث Google Chrome
    echo 3. شغل البرنامج كمدير
    echo 4. تأكد من اتصال الإنترنت
    echo.
) else (
    echo.
    echo ✅ تم تشغيل البرنامج بنجاح!
    echo.
)

pause
